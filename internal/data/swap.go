package data

import (
	"byd_wallet/internal/biz"
	"byd_wallet/internal/biz/base"
	"byd_wallet/model"
	"context"
	"errors"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

func NewSwapRepo(data *Data) biz.SwapRepo {
	return &swapRepo{data}
}

type swapRepo struct {
	*Data
}

func (s *swapRepo) CreateSwapConfig(ctx context.Context, cfg *model.SwapConfig) error {
	return s.DB(ctx).Create(cfg).Error
}

func (s *swapRepo) GetSwapChannelByName(ctx context.Context, name string) (*model.SwapChannel, error) {
	var data model.SwapChannel
	if err := s.DB(ctx).Model(&model.SwapChannel{}).Where("name = ?", name).Take(&data).Error; err != nil {
		return nil, err
	}
	return &data, nil
}

func (s *swapRepo) ListSwapConfig(ctx context.Context) ([]*model.SwapConfig, error) {
	var list []*model.SwapConfig
	if err := s.DB(ctx).Model(&model.SwapConfig{}).Preload("SwapChannel").Find(&list).Error; err != nil {
		return nil, err
	}
	return list, nil
}

func (s *swapRepo) ExistsSwappableToken(ctx context.Context, chainIndex int64, address string) (bool, error) {
	var count int64
	if err := s.DB(ctx).Model(&model.SwappableToken{}).
		Where("chain_index=? AND address=?", chainIndex, address).
		Count(&count).Error; err != nil {
		return false, err
	}
	return count > 0, nil
}

func (s *swapRepo) DefaultSwapChannel(ctx context.Context) (*model.SwapChannel, error) {
	var channel model.SwapChannel
	if err := s.DB(ctx).Model(&model.SwapChannel{}).Take(&channel).Error; err != nil {
		return nil, err
	}
	return &channel, nil
}

func (s *swapRepo) UpdateSwapRecord(ctx context.Context, updated *model.SwapRecord) error {
	return s.DB(ctx).Transaction(func(tx *gorm.DB) error {
		if err := tx.Model(&model.SwapRecord{}).Where("id=?", updated.ID).Updates(map[string]any{
			"status":          updated.Status,
			"gas_fee":         updated.GasFee,
			"block_number":    updated.BlockNumber,
			"finished_at":     updated.FinishedAt,
			"to_token_amount": updated.ToTokenAmount,
		}).Error; err != nil {
			return err
		}
		// create or update details
		for _, detail := range updated.Details {
			var oldDetail model.SwapDetail
			err := tx.Model(&model.SwapDetail{}).
				Where("swap_record_id=?", updated.ID).
				Where("hash=?", detail.Hash).Take(&oldDetail).Error
			if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
				return err
			}
			if errors.Is(err, gorm.ErrRecordNotFound) {
				if err = tx.Create(&detail).Error; err != nil {
					return err
				}
			} else if oldDetail.ID > 0 {
				if err = tx.Model(&model.SwapDetail{}).Where("id=?", oldDetail.ID).Updates(map[string]interface{}{
					"status":    detail.Status,
					"collected": detail.Collected,
				}).Error; err != nil {
					return err
				}
			}
		}
		return nil
	})
}

func (s *swapRepo) ListBlockchainNetwork(ctx context.Context) ([]*model.BlockchainNetwork, error) {
	var networks []*model.SwappableBlockchainNetwork
	if err := s.DB(ctx).Model(&model.SwappableBlockchainNetwork{}).
		Preload("BlockchainNetwork").
		Order("sort_order desc").
		Find(&networks).Error; err != nil {
		return nil, err
	}
	output := make([]*model.BlockchainNetwork, len(networks))
	for i, network := range networks {
		output[i] = network.BlockchainNetwork
	}
	return output, nil
}

func (s *swapRepo) FilterToken(ctx context.Context, filter biz.SwapTokenFilter) ([]*model.TokenAsset, error) {
	var tokens []*model.SwappableToken
	db := s.DB(ctx).Model(&model.SwappableToken{}).
		Joins("TokenAsset")

	if filter.ChainIndex >= 0 {
		db = db.Clauses(clause.Eq{
			Column: clause.Column{
				Table: clause.CurrentTable,
				Name:  "chain_index",
			},
			Value: filter.ChainIndex,
		})
	}
	if filter.SearchKey != "" {
		db = db.Clauses(
			clause.OrConditions{
				Exprs: []clause.Expression{
					clause.Like{
						Column: "TokenAsset.symbol",
						Value:  "%" + filter.SearchKey + "%",
					},
					clause.Eq{
						Column: "TokenAsset.address",
						Value:  filter.SearchKey,
					},
				},
			},
		)
	}
	if err := db.
		Where("enable=? AND display=?", true, true).
		Preload("TokenAsset").
		Find(&tokens).Error; err != nil {
		return nil, err
	}
	var output []*model.TokenAsset
	for _, token := range tokens {
		output = append(output, token.TokenAsset)
	}
	return output, nil
}

func (s *swapRepo) ListToken(ctx context.Context, chainIndex int64) ([]*model.TokenAsset, error) {
	var tokens []*model.SwappableToken
	var args []any
	if chainIndex >= 0 {
		args = []any{"chain_index=?", chainIndex}
	}
	if err := s.DB(ctx).Model(&model.SwappableToken{}).Preload("TokenAsset", args...).Find(&tokens).Error; err != nil {
		return nil, err
	}
	var output []*model.TokenAsset
	for _, token := range tokens {
		if token.TokenAsset == nil {
			continue
		}
		output = append(output, token.TokenAsset)
	}
	return output, nil
}

func (s *swapRepo) CreateSwapRecord(ctx context.Context, record *model.SwapRecord) error {
	return s.DB(ctx).Create(record).Error
}

func (s *swapRepo) GetSwapRecord(ctx context.Context, id uint) (*model.SwapRecord, error) {
	var record model.SwapRecord
	if err := s.DB(ctx).
		Preload("FromTokenAsset").
		Preload("ToTokenAsset").
		Preload("Details").
		Take(&record, id).Error; err != nil {
		return nil, err
	}
	return &record, nil
}

func (s *swapRepo) GetSwapRecordByHash(ctx context.Context, hash string) (*model.SwapRecord, error) {
	var record model.SwapRecord
	if err := s.DB(ctx).
		Preload("FromTokenAsset").
		Preload("ToTokenAsset").
		Preload("Details").
		Take(&record, "hash=?", hash).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &record, nil
}

func (s *swapRepo) ExistsSwapRecord(ctx context.Context, channelID uint, hash string) (bool, error) {
	var count int64
	if err := s.DB(ctx).Model(&model.SwapRecord{}).Where("swap_channel_id=? AND hash=?", channelID, hash).Count(&count).Error; err != nil {
		return false, err
	}
	return count > 0, nil
}

func (s *swapRepo) PagedSwapRecordsByAddresses(ctx context.Context, addresses []string, pagination base.Pagination) ([]*model.SwapRecord, int64, error) {
	var records []*model.SwapRecord
	var count int64
	if err := s.DB(ctx).Scopes(Paginate(pagination)).
		Where("from_address in (?)", addresses).
		Or("to_address in (?)", addresses).
		Preload("FromTokenAsset").
		Preload("ToTokenAsset").
		Preload("Details").
		Order("swapped_at desc").
		Find(&records).
		Offset(-1).Count(&count).
		Error; err != nil {
		return nil, 0, err
	}
	return records, count, nil
}

// ListSwappableTokens 获取指定渠道的可兑换token列表
func (s *swapRepo) ListSwappableTokens(ctx context.Context, channelID uint) ([]*model.SwappableToken, error) {
	var channels []model.SwappableTokenChannel
	if err := s.DB(ctx).Model(&model.SwappableTokenChannel{}).Where("swap_channel_id = ?", channelID).Find(&channels).Error; err != nil {
		return nil, err
	}
	tokenIDs := make([]any, len(channels))
	for i, channel := range channels {
		tokenIDs[i] = channel.SwappableTokenID
	}
	var tokens []*model.SwappableToken
	if err := s.DB(ctx).Model(&model.SwappableToken{}).Where("id IN (?)", tokenIDs).Preload("TokenAsset").Find(&tokens).Error; err != nil {
		return nil, err
	}
	return tokens, nil
}

func (s *swapRepo) ListPendingSwapRecord(ctx context.Context, channelID uint) ([]*model.SwapRecord, error) {
	var list []*model.SwapRecord
	if err := s.DB(ctx).Model(&model.SwapRecord{}).
		Where("swap_channel_id = ?", channelID).
		Where("status NOT IN (?)", model.FinalStatus).
		Preload("FromTokenAsset").
		Preload("ToTokenAsset").
		Preload("Details").
		Find(&list).Error; err != nil {
		return nil, err
	}
	return list, nil
}

// CreateSwappableToken 创建可兑换token
func (s *swapRepo) CreateSwappableToken(ctx context.Context, token *model.SwappableToken) error {
	return s.DB(ctx).Create(token).Error
}

func (s *swapRepo) DisableSwappableToken(ctx context.Context, id uint) error {
	return s.DB(ctx).Model(&model.SwappableToken{}).Where("id=?", id).Updates(map[string]any{
		"enable": false,
	}).Error
}

func (s *swapRepo) EnableSwappableToken(ctx context.Context, id uint) error {
	return s.DB(ctx).Model(&model.SwappableToken{}).Where("id=?", id).Updates(map[string]any{
		"enable": true,
	}).Error
}

func (s *swapRepo) ListAllHotTokens(ctx context.Context) ([]*model.SwappableHotToken, error) {
	var tokens []*model.SwappableHotToken
	if err := s.DB(ctx).Model(&model.SwappableHotToken{}).
		Joins("SwappableToken").
		Clauses(clause.Eq{
			Column: "SwappableToken.enable",
			Value:  true,
		}, clause.Eq{
			Column: "SwappableToken.display",
			Value:  true,
		}).
		Preload("SwappableToken.TokenAsset").
		Order("sort_order asc").
		Find(&tokens).Error; err != nil {
		return nil, err
	}
	return tokens, nil
}

func (s *swapRepo) ListHotTokens(ctx context.Context, chainIndex int64) ([]*model.SwappableHotToken, error) {
	var tokens []*model.SwappableHotToken
	if err := s.DB(ctx).Model(&model.SwappableHotToken{}).
		Joins("SwappableToken").
		Clauses(clause.Eq{
			Column: "SwappableToken.enable",
			Value:  true,
		}, clause.Eq{
			Column: "SwappableToken.display",
			Value:  true,
		}).
		Where(&model.SwappableHotToken{ChainIndex: chainIndex}).
		Preload("SwappableToken.TokenAsset").
		Order("sort_order desc").
		Find(&tokens).Error; err != nil {
		return nil, err
	}
	return tokens, nil
}
