package approval

import (
	"byd_wallet/common"
	"byd_wallet/internal/data"
	"byd_wallet/internal/data/covalenthq"
	"byd_wallet/model"
	"context"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/urfave/cli/v3"
)

var Cmd = &cli.Command{
	Name:  "approval",
	Usage: "授权数据初始化",
	Flags: []cli.Flag{
		&cli.StringFlag{
			Name:     "conf",
			Usage:    "数据库配置文件路径",
			Required: true,
		},
	},
	Action: run,
}

func run(ctx context.Context, cmd *cli.Command) error {
	bc, err := common.NewBootstrapConfig(cmd.String("conf"))
	if err != nil {
		return err
	}
	db, cleanup, err := data.NewGormDB(bc.Data)
	if err != nil {
		return err
	}
	defer cleanup()
	dt := data.NewData(db, nil)
	logger := log.DefaultLogger
	cli, err := data.NewCovalenthqClient(dt, logger)
	if err != nil {
		return err
	}
	f := covalenthq.NewApprovalFetcher(cli)
	repo := data.NewApprovalRepo(dt)
	var uas []model.UserAddress
	if err := db.WithContext(ctx).Model(&model.UserAddress{}).Find(&uas).Error; err != nil {
		return err
	}
	list, err := f.GetApprovalsByAddresses(ctx, uas)
	if err != nil {
		return err
	}

	return repo.CreateOrUpdateApprovals(ctx, list)
}
