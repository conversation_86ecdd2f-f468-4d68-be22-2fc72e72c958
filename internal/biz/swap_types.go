package biz

import (
	"byd_wallet/common/constant"
	"github.com/shopspring/decimal"
)

type MultiQuoteInput struct {
	From, To *SwapToken
	// 滑点百分比，比如传1代表滑点1%
	Slippage decimal.Decimal
}

type MultiQuoteOutput struct {
	Quotes []*QuoteInfo
}

// SwapToken 兑换的Token
type SwapToken struct {
	TokenAssetID uint
	// 链
	ChainIndex int64
	// 代币合约地址
	TokenAddress string
	// 用户地址
	Address string
	// 数量
	Amount decimal.Decimal
	// 精度
	Decimals decimal.Decimal
}

// QuoteInfo 询价信息
type QuoteInfo struct {
	// 基础信息
	Dex                   string          // 交换平台
	DexLogo               string          // 交换平台logo
	FeeRate               decimal.Decimal // 手续费率
	ReceiveTokenAmount    decimal.Decimal // 预估目标代币接收数量
	MinReceiveTokenAmount decimal.Decimal // 最小目标代币接收数量
	EstimatedTime         int             // 预估时间：1(1-3分钟)，2(1-10分钟)，3(5-30分钟) 大于3代表具体的时间， 比如10就是10分钟
	Path                  string          // 交换路径列表

	// 兑换数量限制， 0时代表不限制
	MaxFromTokenAmount, MinFromTokenAmount decimal.Decimal
}

type SwapInput struct {
	From, To *SwapToken
	// 滑点
	Slippage decimal.Decimal
	// 交换路由
	RouterPath string
	// 交换平台名称
	Dex string
}

func (s SwapInput) IsSameChain() bool {
	return s.From != nil && s.To != nil && s.From.ChainIndex == s.To.ChainIndex
}

func (s SwapInput) IsTRONSwap() bool {
	return s.IsSameChain() && s.From.ChainIndex == constant.TronChainIndex
}
func (s SwapInput) IsSolanaSwap() bool {
	return s.IsSameChain() && s.From.ChainIndex == constant.SolChainIndex
}

type SwapOutput struct {
	// 通用字段
	From           string // 调用合约请求地址
	To             string // 被调用合约地址
	Value          string // 调用传入金额
	GasLimit       string
	GasPrice       string // Gas费用
	Data           string // 合约调用数据
	ApproveAddress string // 授权地址
	ToType         string // 存币类型
	SwapPrice      string // 实时汇率

	// SWFT通道特有字段
	PlatformAddr string // 平台地址

	// 第三方原始数据
	RawData string
	OrderId string
}

type SwapTokenFilter struct {
	ChainIndex int64
	SearchKey  string // token合约号或者symbol模糊搜索
}

type AddSwapRecordInput struct {
	From, To *SwapToken
	// 滑点
	Slippage decimal.Decimal
	// 交换路由
	RouterPath string
	// 交换平台名称
	Dex string
	// 客户端上传的交易hash
	Hash string
	// 授权hash
	ApprovalHash string
	// 汇率
	SwapPrice string
	// 费率
	FeeRate decimal.Decimal
	// 预估时间，1(1-3分钟)，2(1-10分钟)，3(5-30分钟)
	EstimatedTime string
	// 矿工费
	GasFee  string
	OrderId string
}

type CreateSwapConfigInput struct {
	SwapChannelName      string
	CronSpec             string
	RecordMaxConcurrency int
}
