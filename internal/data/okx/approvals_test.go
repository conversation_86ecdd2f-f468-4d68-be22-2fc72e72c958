package okx

import (
	"context"
	"encoding/json"
	"fmt"
	"testing"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/stretchr/testify/require"
)

func TestClient_GetApprovals(t *testing.T) {
	cli := NewClient(&Config{
		BaseURL:    "https://web3.okx.com",
		APIKey:     "f2f528f6-5720-4800-be46-38d03f7cc732",
		SecretKey:  "5D4695712A18F097822504040A54B36F",
		Passphrase: "r!KetW#=&LJ1gbb",
		ProjectID:  "ae1352666d6a98698588e0b665c493fb",
		Timeout:    time.Second * 3,
	}, log.DefaultLogger)
	resp, err := cli.GetApprovals(context.Background(), &GetApprovalsRequest{
		AddressList: []*Address{
			{
				Address:    "0x6DAb26B3261573180c4C65B6D1E75a8088342ae1",
				ChainIndex: "1",
			},
			{
				Address:    "0x6DAb26B3261573180c4C65B6D1E75a8088342ae1",
				ChainIndex: "56",
			},
			{
				Address:    "0xD2B3670F10525645118206e63F321A3bc4D37bC2",
				ChainIndex: "56",
			},
		},
		Limit:  "10",
		Cursor: "1",
	})
	require.NoError(t, err)
	// assert.Equal(t, "1", resp.ChainIndex)
	// assert.Len(t, resp.ApprovalProjects, 3)
	b, _ := json.Marshal(resp)
	fmt.Println(string(b))
}
