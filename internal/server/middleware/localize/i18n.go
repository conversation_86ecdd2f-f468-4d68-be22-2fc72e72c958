// Package localize https://github.com/go-kratos/examples/blob/main/i18n/README.md
package localize

import (
	"byd_wallet/common/lang"
	"context"
	"embed"
	"fmt"
	"github.com/BurntSushi/toml"
	"github.com/go-kratos/kratos/v2/middleware"
	"github.com/go-kratos/kratos/v2/transport"
	"github.com/nicksnyder/go-i18n/v2/i18n"
	"golang.org/x/text/language"
)

//go:embed locale.*.toml
var LocaleFS embed.FS
var Bundle *i18n.Bundle

func init() {
	Bundle = i18n.NewBundle(language.English)
	Bundle.RegisterUnmarshalFunc("toml", toml.Unmarshal)

	locales := []string{"en", "zh"}
	for _, locale := range locales {
		if _, err := Bundle.LoadMessageFileFS(LocaleFS, fmt.Sprintf("locale.%s.toml", locale)); err != nil {
			panic(err)
		}
	}
}

func I18N() middleware.Middleware {
	return func(handler middleware.Handler) middleware.Handler {
		return func(ctx context.Context, req interface{}) (reply interface{}, err error) {
			if tr, ok := transport.FromServerContext(ctx); ok {
				accept := tr.RequestHeader().Get("accept-language")
				//println(accept)
				ctx = lang.NewContext(ctx, accept)
			}
			return handler(ctx, req)
		}
	}
}
