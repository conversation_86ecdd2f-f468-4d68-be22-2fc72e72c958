package metapath

import (
	"context"
	"fmt"
	"net/http"
)

// Swap 获取交换信息
func (c *Client) Swap(ctx context.Context, req *SwapRequest) (SwapResult, error) {
	if err := ValidateStruct(req); err != nil {
		return nil, fmt.Errorf("invalid params: %w", err)
	}

	var response SwapResponse
	if err := c.call(ctx, http.MethodPost, "/api/commonSwap", req, &response); err != nil {
		return nil, err
	}
	if err := response.Error(); err != nil {
		return nil, err
	}
	switch req.Dex {
	case Bridgers:
		if req.IsSolanaTx() {
			data, err := response.GetSolanaSwapTxData()
			if err != nil {
				return nil, err
			}
			if !data.IsEmpty() {
				return data, nil
			}
		}
		if req.IsTRONTx() {
			data, err := response.GetBridgersTRONSwapTxData()
			if err != nil {
				return nil, err
			}
			if !data.IsEmpty() {
				return data, nil
			}
		}
	case Aggregator:
		if req.IsSameTRONTx() {
			data, err := response.GetAggregatorTRONSwapResultData()
			if err != nil {
				return nil, err
			}
			if !data.IsEmpty() {
				return data, nil
			}
		}
	case SWFT:
		return response.GetSWFTSwapTxData()
	case SunSwapV2:
		if req.IsSameTRONTx() {
			return response.GetAggregatorTRONSwapResultData()
		}
	}
	return response.GetEvmSwapTxData()
}
