package metapath

import (
	"encoding/json"
	"github.com/shopspring/decimal"
)

type AggregatorTRONSwapResultData struct {
	TxData *AggregatorTRONSwapTxData `json:"txData"`
}

type AggregatorTRONSwapTxData struct {
	TransferData *TransferData      `json:"transferData"`
	To           string             `json:"to"`
	Value        string             `json:"value"`
	Data         AggregatorTRONData `json:"data"`
}

func (a AggregatorTRONSwapTxData) GetTransferData() *TransferData {
	return a.TransferData
}

func (a AggregatorTRONSwapTxData) IsEmpty() bool {
	return a.Data.Transaction.RawDataHex == ""
}

type AggregatorTRONData struct {
	Transaction TRONTransaction `json:"transaction"`
}

type TRONTransaction struct {
	RawDataHex string `json:"raw_data_hex"`
}

func (r *SwapResponse) GetAggregatorTRONSwapResultData() (*AggregatorTRONSwapTxData, error) {
	var result AggregatorTRONSwapResultData
	err := json.Unmarshal(r.Data, &result)
	return result.TxData, err
}

type BridgersTRONSwapResultData struct {
	TxData *BridgersTRONSwapTxData `json:"txData"`
}

type BridgersTRONSwapTxData struct {
	TransferData      *TransferData   `json:"transferData"`
	TronRouterAddrees string          `json:"tronRouterAddrees"`
	FunctionName      string          `json:"functionName"`
	Parameter         []TRONParameter `json:"parameter"`
	Options           TRONOptions     `json:"options"`
	FromAddress       string          `json:"fromAddress"`
	To                string          `json:"to"`
}

type TRONParameter struct {
	Type  string `json:"type"`
	Value any    `json:"value"`
}

type TRONOptions struct {
	FeeLimit  decimal.Decimal `json:"feeLimit"`
	CallValue string          `json:"callValue"`
}

func (b BridgersTRONSwapTxData) GetTransferData() *TransferData {
	return b.TransferData
}

func (b BridgersTRONSwapTxData) IsEmpty() bool {
	return b.TronRouterAddrees == ""
}

func (b BridgersTRONSwapTxData) ParameterJSON() string {
	var params []map[string]any
	for _, p := range b.Parameter {
		params = append(params, map[string]any{
			p.Type: p.Value,
		})
	}
	bytes, _ := json.Marshal(&params)
	return string(bytes)
}

func (r *SwapResponse) GetBridgersTRONSwapTxData() (*BridgersTRONSwapTxData, error) {
	var result BridgersTRONSwapResultData
	err := json.Unmarshal(r.Data, &result)
	return result.TxData, err
}
