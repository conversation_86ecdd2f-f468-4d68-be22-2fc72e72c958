package data

import (
	"byd_wallet/common/constant"
	"byd_wallet/internal/biz"
	"byd_wallet/internal/biz/base"
	"byd_wallet/internal/biz/dbtx"
	"byd_wallet/internal/biz/gaspool"
	gpbase "byd_wallet/internal/biz/gaspool/base"
	"byd_wallet/internal/conf"
	"byd_wallet/internal/data/covalenthq"
	"byd_wallet/internal/data/etherscan"
	"byd_wallet/internal/data/metapath"
	"byd_wallet/internal/data/tronify"
	"context"
	"crypto/tls"
	"fmt"
	"strings"
	"time"

	"github.com/google/wire"
	"github.com/redis/go-redis/v9"
	"gorm.io/driver/mysql"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

var ProviderSet = wire.NewSet(
	NewRedisClient,
	NewGormDB,
	NewData, NewDBTx,

	NewTokenAssetRepo,
	NewTokenAssetStarRepo,
	NewMarketRepo,
	NewTronRentRepo,
	tronify.NewTronRentClient,
	NewWeidubotTronRentRequester,
	NewTronRentRequesterFactory,
	NewBlockchainNetworkCache,
	NewRPCEndpointRepo,
	NewCoinDataThirdAPI,
	NewDappRepo, NewDappAdminRepo,
	NewCurrencyRateAPI,
	NewS3Repo,
	NewUserAddressRepo,
	NewUserRepo,
	NewAdminJwt,
	NewAdminRepo,
	NewBlockchainNetworkRepo,
	NewTransactionRepo,
	NewTokenContractAPI,
	NewTokenContractRepo,
	NewTxProcessor,
	NewTransactionBatcher,
	NewChainSyncerRepo,
	NewEVMInternalTxSyncerRepo,
	NewApprovalRepo,
	NewUserHoldTokenRepo,
	NewTxSyncEventPublisher,

	NewCovalenthqClient,
	covalenthq.NewApprovalFetcher,
	NewOKXClient,
	NewOKXChainIndexDto,
	NewEtherscanClient,
	etherscan.NewEvmInternalTxFetcher,
	NewSwapRepo,
	metapath.NewTokenSwapper, NewMetapathConfig, metapath.NewClient, metapath.NewSwapTokenFetcher,
	NewEvmChainClient,
	NewTronChainClient,
	NewSolanaChainClient,
	NewSpotPriceRepo, wire.Bind(new(biz.SpotPriceRepo), new(*spotPriceRepo)),
	NewSwapAdminRepo,
	NewCommonSortRepo,

	// ---gas pool
	NewGasPoolRepo, wire.Bind(new(gaspool.Repo), new(*gasPoolRepo)),
	NewGasPoolUserLocker,
	NewGasPoolHotAccountReader,
	wire.Bind(new(gpbase.GasPoolSponsorTxMgr), new(*gasPoolRepo)),
	wire.Bind(new(gpbase.TokenPriceReader), new(*spotPriceRepo)),
	wire.Bind(new(gpbase.GasPoolMgr), new(*gasPoolRepo)),
	NewTronAddressChecker,
	NewTronBandwidthPayWallet,
	NewTronRentApi,
	NewTronAsyncTaskMgr,
	// 添加EVM Paymaster RepoFactory提供者
	// 修复Wire依赖注入错误：no provider found for byd_wallet/internal/biz/gaspool/paymaster/evm.RepoFactory
	// RepoFactory是函数类型，不需要使用wire.Bind，直接提供即可
	NewEvmPaymasterRepoFactory,
	// 添加MegaFuel客户端提供者
	NewMegaFuelPaymaster,
)

type Data struct {
	db *gorm.DB
	rd redis.UniversalClient
}

func NewData(db *gorm.DB, rd redis.UniversalClient) *Data {
	return &Data{
		db: db,
		rd: rd,
	}
}

func NewDBTx(d *Data) dbtx.DBTx {
	return d
}

type contextTxKey struct{}

func (d Data) ExecTx(ctx context.Context, fn func(ctx context.Context) error) error {
	return d.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		ctx = context.WithValue(ctx, contextTxKey{}, tx)
		return fn(ctx)
	})
}

func (d Data) DB(ctx context.Context) *gorm.DB {
	tx, ok := d.Tx(ctx)
	if ok {
		return tx
	}
	return d.db.WithContext(ctx)
}

func (d Data) Tx(ctx context.Context) (*gorm.DB, bool) {
	tx, ok := ctx.Value(contextTxKey{}).(*gorm.DB)
	return tx, ok
}

func NewRedisClient(c *conf.Data) (redis.UniversalClient, func(), error) {
	opts := &redis.UniversalOptions{
		Addrs:         c.Redis.Addrs,
		Username:      c.Redis.Username,
		Password:      c.Redis.Password,
		DB:            int(c.Redis.Db),
		DialTimeout:   c.Redis.DialTimeout.AsDuration(),
		WriteTimeout:  c.Redis.WriteTimeout.AsDuration(),
		ReadTimeout:   c.Redis.ReadTimeout.AsDuration(),
		IsClusterMode: c.Redis.IsClusterMode,
	}
	if c.Redis.IsClusterMode {
		opts.TLSConfig = &tls.Config{
			InsecureSkipVerify: true,
		}
	}
	rdb := redis.NewUniversalClient(opts)
	if err := rdb.Ping(context.Background()).Err(); err != nil {
		_ = rdb.Close()
		return nil, nil, fmt.Errorf("redis ping: %w", err)
	}
	return rdb, func() {
		_ = rdb.Close()
	}, nil
}

func NewGormDB(c *conf.Data) (*gorm.DB, func(), error) {
	connMaxLifeTime, err := time.ParseDuration(c.Database.ConnMaxLifeTime)
	if err != nil {
		return nil, nil, fmt.Errorf("parse Database.ConnMaxLifeTime fail: %w", err)
	}

	var dialector gorm.Dialector

	switch c.Database.Driver {
	case "mysql":
		dialector = mysql.New(mysql.Config{
			DSN: fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?charset=utf8mb4&parseTime=True&loc=Local",
				c.Database.DbUser,
				c.Database.DbPassword,
				c.Database.DbHost,
				c.Database.DbPort,
				c.Database.DbName,
			),
		})
	case "postgres":
		dialector = postgres.New(postgres.Config{
			DSN: fmt.Sprintf("host=%s user=%s password=%s dbname=%s port=%d ",
				c.Database.DbHost,
				c.Database.DbUser,
				c.Database.DbPassword,
				c.Database.DbName,
				c.Database.DbPort,
			),
		})
	default:
		return nil, nil, fmt.Errorf("unsupported driver: %s", c.Database.Driver)
	}
	db, err := gorm.Open(dialector, &gorm.Config{
		DryRun: c.Database.DryRun,
		Logger: logger.Default.LogMode(logger.Error),
	})
	if err != nil {
		return nil, nil, err
	}
	sqlDb, err := db.DB()
	if err != nil {
		return nil, nil, err
	}
	sqlDb.SetMaxIdleConns(int(c.Database.MaxIdleConns))
	sqlDb.SetMaxOpenConns(int(c.Database.MaxOpenConns))
	sqlDb.SetConnMaxLifetime(connMaxLifeTime)

	// NOTE: config tracing
	// db.Use(gormopentracing.New())

	return db, func() {
		_ = sqlDb.Close()
	}, nil
}

func Paginate(pagination base.Pagination) func(db *gorm.DB) *gorm.DB {
	page := pagination.Page
	pageSize := pagination.PageSize
	return func(db *gorm.DB) *gorm.DB {
		if page <= 0 {
			page = 1
		}
		offset := (page - 1) * pageSize
		return db.Offset(offset).Limit(pageSize)
	}
}

// --- okx client
// api request: chain index // https://github.com/trustwallet/wallet-core/blob/master/docs/registry.md
// okx request: chain index // https://web3.okx.com/zh-hans/build/docs/waas/walletapi-resources-supported-networks

type OKXChainIndexDto struct {
	api2Okx map[int64]string
	okx2Api map[string]int64
}

func NewOKXChainIndexDto() *OKXChainIndexDto {
	dto := &OKXChainIndexDto{
		api2Okx: map[int64]string{
			constant.BtcChainIndex:      "0",
			constant.EthChainIndex:      "1",
			constant.SolChainIndex:      "501",
			constant.BscChainIndex:      "56",
			constant.PolChainIndex:      "137",
			constant.BaseChainIndex:     "8453",
			constant.ArbChainIndex:      "42161",
			constant.OptimismChainIndex: "10",
			constant.TronChainIndex:     "195",
		},
		okx2Api: map[string]int64{
			"0":     constant.BtcChainIndex,
			"1":     constant.EthChainIndex,
			"501":   constant.SolChainIndex,
			"56":    constant.BscChainIndex,
			"137":   constant.PolChainIndex,
			"8453":  constant.BaseChainIndex,
			"42161": constant.ArbChainIndex,
			"10":    constant.OptimismChainIndex,
			"195":   constant.TronChainIndex,
		},
	}
	return dto
}

func (d *OKXChainIndexDto) ApiChainIndex(okxChainIndex string) int64 {
	return d.okx2Api[okxChainIndex]
}

func (d *OKXChainIndexDto) OkxChainIndex(apiChainIndex int64) string {
	return d.api2Okx[apiChainIndex]
}

func (d *OKXChainIndexDto) ChainIndexReqParam(apiChainIndexes []int64) string {
	sb := strings.Builder{}

	for _, v := range apiChainIndexes {
		if sb.Len() > 0 {
			sb.WriteRune(',')
		}
		vv, ok := d.api2Okx[v]
		if !ok {
			continue
		}
		sb.WriteString(vv)
	}

	return sb.String()
}
