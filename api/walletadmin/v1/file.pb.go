// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: api/walletadmin/v1/file.proto

package v1

import (
	_ "buf.build/gen/go/bufbuild/protovalidate/protocolbuffers/go/buf/validate"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type GeneratePresignedRequestReq struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 业务类型(dapp_logo,token_logo)
	BizType string `protobuf:"bytes,1,opt,name=biz_type,json=bizType,proto3" json:"biz_type,omitempty"`
	// 文件后缀名：jpg,png...
	FileSuffix    string `protobuf:"bytes,2,opt,name=file_suffix,json=fileSuffix,proto3" json:"file_suffix,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GeneratePresignedRequestReq) Reset() {
	*x = GeneratePresignedRequestReq{}
	mi := &file_api_walletadmin_v1_file_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GeneratePresignedRequestReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GeneratePresignedRequestReq) ProtoMessage() {}

func (x *GeneratePresignedRequestReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_file_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GeneratePresignedRequestReq.ProtoReflect.Descriptor instead.
func (*GeneratePresignedRequestReq) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_file_proto_rawDescGZIP(), []int{0}
}

func (x *GeneratePresignedRequestReq) GetBizType() string {
	if x != nil {
		return x.BizType
	}
	return ""
}

func (x *GeneratePresignedRequestReq) GetFileSuffix() string {
	if x != nil {
		return x.FileSuffix
	}
	return ""
}

type GeneratePresignedRequestReply struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	Url   string                 `protobuf:"bytes,1,opt,name=url,proto3" json:"url,omitempty"`
	// 请求方法: GET/POST/PUT...
	Method string `protobuf:"bytes,2,opt,name=method,proto3" json:"method,omitempty"`
	// 请求头
	Headers       []*Header `protobuf:"bytes,3,rep,name=headers,proto3" json:"headers,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GeneratePresignedRequestReply) Reset() {
	*x = GeneratePresignedRequestReply{}
	mi := &file_api_walletadmin_v1_file_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GeneratePresignedRequestReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GeneratePresignedRequestReply) ProtoMessage() {}

func (x *GeneratePresignedRequestReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_file_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GeneratePresignedRequestReply.ProtoReflect.Descriptor instead.
func (*GeneratePresignedRequestReply) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_file_proto_rawDescGZIP(), []int{1}
}

func (x *GeneratePresignedRequestReply) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *GeneratePresignedRequestReply) GetMethod() string {
	if x != nil {
		return x.Method
	}
	return ""
}

func (x *GeneratePresignedRequestReply) GetHeaders() []*Header {
	if x != nil {
		return x.Headers
	}
	return nil
}

type Header struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Key           string                 `protobuf:"bytes,1,opt,name=key,proto3" json:"key,omitempty"`
	Value         []string               `protobuf:"bytes,2,rep,name=value,proto3" json:"value,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Header) Reset() {
	*x = Header{}
	mi := &file_api_walletadmin_v1_file_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Header) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Header) ProtoMessage() {}

func (x *Header) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_file_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Header.ProtoReflect.Descriptor instead.
func (*Header) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_file_proto_rawDescGZIP(), []int{2}
}

func (x *Header) GetKey() string {
	if x != nil {
		return x.Key
	}
	return ""
}

func (x *Header) GetValue() []string {
	if x != nil {
		return x.Value
	}
	return nil
}

var File_api_walletadmin_v1_file_proto protoreflect.FileDescriptor

const file_api_walletadmin_v1_file_proto_rawDesc = "" +
	"\n" +
	"\x1dapi/walletadmin/v1/file.proto\x12\x12api.walletadmin.v1\x1a\x1cgoogle/api/annotations.proto\x1a\x1bbuf/validate/validate.proto\"b\n" +
	"\x1bGeneratePresignedRequestReq\x12\"\n" +
	"\bbiz_type\x18\x01 \x01(\tB\a\xbaH\x04r\x02\x10\x01R\abizType\x12\x1f\n" +
	"\vfile_suffix\x18\x02 \x01(\tR\n" +
	"fileSuffix\"\x7f\n" +
	"\x1dGeneratePresignedRequestReply\x12\x10\n" +
	"\x03url\x18\x01 \x01(\tR\x03url\x12\x16\n" +
	"\x06method\x18\x02 \x01(\tR\x06method\x124\n" +
	"\aheaders\x18\x03 \x03(\v2\x1a.api.walletadmin.v1.HeaderR\aheaders\"0\n" +
	"\x06Header\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x03(\tR\x05value2\xa8\x01\n" +
	"\aFileSrv\x12\x9c\x01\n" +
	"\x18GeneratePresignedRequest\x12/.api.walletadmin.v1.GeneratePresignedRequestReq\x1a1.api.walletadmin.v1.GeneratePresignedRequestReply\"\x1c\x82\xd3\xe4\x93\x02\x16:\x01*\"\x11/admin/v1/presignB\xaf\x01\n" +
	"\x16com.api.walletadmin.v1B\tFileProtoP\x01Z byd_wallet/api/walletadmin/v1;v1\xa2\x02\x03AWX\xaa\x02\x12Api.Walletadmin.V1\xca\x02\x12Api\\Walletadmin\\V1\xe2\x02\x1eApi\\Walletadmin\\V1\\GPBMetadata\xea\x02\x14Api::Walletadmin::V1b\x06proto3"

var (
	file_api_walletadmin_v1_file_proto_rawDescOnce sync.Once
	file_api_walletadmin_v1_file_proto_rawDescData []byte
)

func file_api_walletadmin_v1_file_proto_rawDescGZIP() []byte {
	file_api_walletadmin_v1_file_proto_rawDescOnce.Do(func() {
		file_api_walletadmin_v1_file_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_api_walletadmin_v1_file_proto_rawDesc), len(file_api_walletadmin_v1_file_proto_rawDesc)))
	})
	return file_api_walletadmin_v1_file_proto_rawDescData
}

var file_api_walletadmin_v1_file_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_api_walletadmin_v1_file_proto_goTypes = []any{
	(*GeneratePresignedRequestReq)(nil),   // 0: api.walletadmin.v1.GeneratePresignedRequestReq
	(*GeneratePresignedRequestReply)(nil), // 1: api.walletadmin.v1.GeneratePresignedRequestReply
	(*Header)(nil),                        // 2: api.walletadmin.v1.Header
}
var file_api_walletadmin_v1_file_proto_depIdxs = []int32{
	2, // 0: api.walletadmin.v1.GeneratePresignedRequestReply.headers:type_name -> api.walletadmin.v1.Header
	0, // 1: api.walletadmin.v1.FileSrv.GeneratePresignedRequest:input_type -> api.walletadmin.v1.GeneratePresignedRequestReq
	1, // 2: api.walletadmin.v1.FileSrv.GeneratePresignedRequest:output_type -> api.walletadmin.v1.GeneratePresignedRequestReply
	2, // [2:3] is the sub-list for method output_type
	1, // [1:2] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_api_walletadmin_v1_file_proto_init() }
func file_api_walletadmin_v1_file_proto_init() {
	if File_api_walletadmin_v1_file_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_api_walletadmin_v1_file_proto_rawDesc), len(file_api_walletadmin_v1_file_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_walletadmin_v1_file_proto_goTypes,
		DependencyIndexes: file_api_walletadmin_v1_file_proto_depIdxs,
		MessageInfos:      file_api_walletadmin_v1_file_proto_msgTypes,
	}.Build()
	File_api_walletadmin_v1_file_proto = out.File
	file_api_walletadmin_v1_file_proto_goTypes = nil
	file_api_walletadmin_v1_file_proto_depIdxs = nil
}
