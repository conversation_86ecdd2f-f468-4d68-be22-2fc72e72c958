// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package main

import (
	"byd_wallet/internal/biz"
	"byd_wallet/internal/biz/dapp"
	"byd_wallet/internal/biz/gaspool"
	"byd_wallet/internal/biz/gaspool/paymaster"
	"byd_wallet/internal/biz/gaspool/paymaster/bsc"
	"byd_wallet/internal/biz/gaspool/paymaster/evm"
	"byd_wallet/internal/biz/gaspool/paymaster/solana"
	tron2 "byd_wallet/internal/biz/gaspool/paymaster/tron"
	"byd_wallet/internal/biz/rent"
	"byd_wallet/internal/biz/syncer/chain/tron"
	"byd_wallet/internal/conf"
	"byd_wallet/internal/data"
	"byd_wallet/internal/data/covalenthq"
	"byd_wallet/internal/data/metapath"
	"byd_wallet/internal/data/tronify"
	"byd_wallet/internal/server"
	"byd_wallet/internal/service"
	"github.com/go-kratos/kratos/v2"
	"github.com/go-kratos/kratos/v2/log"
)

import (
	_ "go.uber.org/automaxprocs"
)

// Injectors from wire.go:

// wireApp init kratos application.
func wireApp(confServer *conf.Server, confData *conf.Data, logger log.Logger) (*kratos.App, func(), error) {
	db, cleanup, err := data.NewGormDB(confData)
	if err != nil {
		return nil, nil, err
	}
	universalClient, cleanup2, err := data.NewRedisClient(confData)
	if err != nil {
		cleanup()
		return nil, nil, err
	}
	dataData := data.NewData(db, universalClient)
	userRepo := data.NewUserRepo(dataData)
	client, err := data.NewCovalenthqClient(dataData, logger)
	if err != nil {
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	approvalFetcher := covalenthq.NewApprovalFetcher(client)
	approvalRepo := data.NewApprovalRepo(dataData)
	userUsecase := biz.NewUserUsecase(logger, db, universalClient, userRepo, approvalFetcher, approvalRepo)
	userService := service.NewUserService(userUsecase)
	walletUsecase := biz.NewWalletUsecase(logger, db)
	okxClient, err := data.NewOKXClient(dataData, logger)
	if err != nil {
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	okxChainIndexDto := data.NewOKXChainIndexDto()
	tokenAssetRepo := data.NewTokenAssetRepo(logger, dataData, okxClient, okxChainIndexDto)
	tokenAssetUsecase := biz.NewTokenAssetUsecase(logger, tokenAssetRepo)
	blockchainNetworkRepo := data.NewBlockchainNetworkRepo(dataData)
	blockchainNetworkUsecase := biz.NewBlockchainNetworkUsecase(logger, blockchainNetworkRepo)
	userHoldTokenRepo := data.NewUserHoldTokenRepo(dataData)
	userHoldTokenUsecase := biz.NewUserHoldTokenUsecase(userHoldTokenRepo, logger)
	walletSrvService := service.NewWalletSrvService(walletUsecase, tokenAssetUsecase, blockchainNetworkUsecase, userHoldTokenUsecase)
	tronRentRepo := data.NewTronRentRepo(dataData)
	tronRentRequester := tronify.NewTronRentClient(logger)
	rpcEndpointRepo := data.NewRPCEndpointRepo(dataData)
	roundRobinClient, cleanup3, err := data.NewTronChainClient(rpcEndpointRepo)
	if err != nil {
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	weidubotTronRentRequester, err := data.NewWeidubotTronRentRequester(logger, roundRobinClient, dataData)
	if err != nil {
		cleanup3()
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	tronRentRequesterFactory := data.NewTronRentRequesterFactory(tronRentRequester, weidubotTronRentRequester)
	tronRentUseCase := rent.NewTronRentUseCase(tronRentRepo, tronRentRequesterFactory, logger)
	tronService := service.NewTronService(tronRentUseCase)
	coinDataThirdAPI, err := data.NewCoinDataThirdAPI(dataData)
	if err != nil {
		cleanup3()
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	marketRepo := data.NewMarketRepo(dataData, logger, coinDataThirdAPI)
	marketUsecase := biz.NewMarketUsecase(logger, marketRepo)
	marketService := service.NewMarketService(marketUsecase)
	repo := data.NewDappRepo(dataData, okxClient, okxChainIndexDto)
	dbTx := data.NewDBTx(dataData)
	s3Repo, err := data.NewS3Repo(dataData)
	if err != nil {
		cleanup3()
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	usecase := dapp.NewUsecase(repo, dbTx, s3Repo, logger)
	approvalUsecase := biz.NewApprovalUsecase(approvalRepo, blockchainNetworkRepo)
	dappService := service.NewDappService(usecase, approvalUsecase)
	swapRepo := data.NewSwapRepo(dataData)
	config, err := data.NewMetapathConfig(dataData)
	if err != nil {
		cleanup3()
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	metapathClient := metapath.NewClient(config, logger)
	tokenSwapper := metapath.NewTokenSwapper(metapathClient, roundRobinClient, logger)
	transactionRepo := data.NewTransactionRepo(dataData)
	swapTokenFetcher := metapath.NewSwapTokenFetcher(metapathClient, logger)
	multiChainClient, cleanup4, err := data.NewEvmChainClient(rpcEndpointRepo)
	if err != nil {
		cleanup3()
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	smartNodeSelectionClient, cleanup5, err := data.NewSolanaChainClient(rpcEndpointRepo)
	if err != nil {
		cleanup4()
		cleanup3()
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	tokenContractRepo := data.NewTokenContractRepo(logger, multiChainClient, roundRobinClient, smartNodeSelectionClient)
	tokenCollector := biz.NewTokenCollector(tokenAssetRepo, tokenContractRepo, s3Repo)
	transactionFetcher := tron.NewTransactionFetcher(roundRobinClient)
	bizTransactionFetcher := biz.NewTransactionFetcher(transactionFetcher)
	swapUsecase := biz.NewSwapUsecase(logger, swapRepo, tokenSwapper, tokenAssetRepo, transactionRepo, swapTokenFetcher, tokenCollector, coinDataThirdAPI, blockchainNetworkRepo, bizTransactionFetcher)
	swapService := service.NewSwapService(swapUsecase)
	gasPoolRepo := data.NewGasPoolRepo(dataData)
	userLocker := data.NewGasPoolUserLocker(dataData)
	spotPriceRepo, err := data.NewSpotPriceRepo(logger, dataData)
	if err != nil {
		cleanup5()
		cleanup4()
		cleanup3()
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	hotAccountReader := data.NewGasPoolHotAccountReader()
	megafuelPaymaster, err := data.NewMegaFuelPaymaster(dataData)
	if err != nil {
		cleanup5()
		cleanup4()
		cleanup3()
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	bscPaymaster := bsc.NewPaymaster(logger, spotPriceRepo, hotAccountReader, multiChainClient, gasPoolRepo, megafuelPaymaster)
	repoFactory := data.NewEvmPaymasterRepoFactory(dataData)
	paymasterBuilder := evm.NewPaymasterBuilder(logger, spotPriceRepo, hotAccountReader, multiChainClient, repoFactory, gasPoolRepo)
	solanaPaymaster := solana.NewPaymaster(logger, spotPriceRepo, hotAccountReader, smartNodeSelectionClient)
	tronBandwidthPayWallet := data.NewTronBandwidthPayWallet()
	tronRentApi := data.NewTronRentApi()
	tronAddressChecker := data.NewTronAddressChecker()
	asyncTaskMgr := data.NewTronAsyncTaskMgr()
	tronPaymaster := tron2.NewPaymaster(logger, spotPriceRepo, gasPoolRepo, gasPoolRepo, roundRobinClient, tronBandwidthPayWallet, tronRentApi, tronAddressChecker, asyncTaskMgr)
	paymasterFactory := paymaster.NewPaymasterFactory(bscPaymaster, paymasterBuilder, solanaPaymaster, tronPaymaster)
	gaspoolUsecase := gaspool.NewUsecase(logger, gasPoolRepo, userLocker, paymasterFactory)
	gasPoolService := service.NewGasPoolService(gaspoolUsecase)
	httpServer := server.NewHTTPServer(confServer, userService, walletSrvService, tronService, marketService, dappService, swapService, gasPoolService, logger)
	app := newApp(logger, httpServer)
	return app, func() {
		cleanup5()
		cleanup4()
		cleanup3()
		cleanup2()
		cleanup()
	}, nil
}
