package biz

import (
	"byd_wallet/model"
	"context"
	"time"
)

type ApprovalFetcher interface {
	GetApprovalsByAddresses(ctx context.Context, addresses []model.UserAddress) ([]*model.Approval, error)
}

type ApprovalRepo interface {
	// CreateOrUpdateApprovals 创建授权记录，如果存在则更新
	CreateOrUpdateApprovals(ctx context.Context, approvals []*model.Approval) error
	// ListApprovalByUserAddress 查询单个用户钱包地址授权记录
	ListApprovalByUserAddress(ctx context.Context, address model.UserAddress) (*TokenDappApprovals, error)
	// ListApprovedDappsByUserAddresses 根据用户地址查询已授权的dapp
	ListApprovedDappsByUserAddresses(ctx context.Context, addresses []model.UserAddress) ([]*UserApprovedDapp, error)
	// ListTokenApprovalsByDapp 根据dapp应用查询授权信息
	ListTokenApprovalsByDapp(ctx context.Context, dapp *UserApprovedDapp) ([]*TokenApproval, error)
	// ListApprovedAddresses 根据用户地址筛选出已授权的地址
	ListApprovedAddresses(ctx context.Context, addresses []model.UserAddress) ([]model.UserAddress, error)
}

type TokenApproval struct {
	Token    *model.TokenAsset
	Approval *model.Approval
}

// UserApprovedDapp 用户已授权的dapp
type UserApprovedDapp struct {
	Addresses         []string
	Dapp              *model.Dapp
	BlockchainNetwork *model.BlockchainNetwork
	Spender           string
	UpdatedAt         time.Time
}

type UserApprovedDappsByUpdatedAt []*UserApprovedDapp

func (u UserApprovedDappsByUpdatedAt) Len() int { return len(u) }

func (u UserApprovedDappsByUpdatedAt) Less(i, j int) bool {
	return u[i].UpdatedAt.After(u[j].UpdatedAt)
}

func (u UserApprovedDappsByUpdatedAt) Swap(i, j int) { u[i], u[j] = u[j], u[i] }

// TokenDappApprovals token授权信息
type TokenDappApprovals struct {
	Address           model.UserAddress
	Network           *model.BlockchainNetwork
	TokenDappApproval []*TokenDappApproval
}

type TokenDappApproval struct {
	Dapp     *model.Dapp
	Token    *model.TokenAsset
	Approval *model.Approval
}

type ApprovalUsecase struct {
	repo        ApprovalRepo
	networkRepo BlockchainNetworkRepo
}

func NewApprovalUsecase(repo ApprovalRepo, networkRepo BlockchainNetworkRepo) *ApprovalUsecase {
	return &ApprovalUsecase{repo: repo, networkRepo: networkRepo}
}

func (uc ApprovalUsecase) ListApprovedAddresses(ctx context.Context, addresses []model.UserAddress) ([]model.UserAddress, error) {
	return uc.repo.ListApprovedAddresses(ctx, addresses)
}

func (uc ApprovalUsecase) ListApprovalByUserAddress(ctx context.Context, address model.UserAddress) (*TokenDappApprovals, error) {
	return uc.repo.ListApprovalByUserAddress(ctx, address)
}

func (uc ApprovalUsecase) ListApprovedDappsByUserAddresses(ctx context.Context, addresses []model.UserAddress) ([]*UserApprovedDapp, error) {
	return uc.repo.ListApprovedDappsByUserAddresses(ctx, addresses)
}

func (uc ApprovalUsecase) ListTokenApprovalsByDapp(ctx context.Context, dapp *UserApprovedDapp) ([]*TokenApproval, error) {
	return uc.repo.ListTokenApprovalsByDapp(ctx, dapp)
}
