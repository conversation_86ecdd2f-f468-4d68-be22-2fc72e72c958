package data

import (
	"byd_wallet/internal/biz/gaspool"
	"byd_wallet/internal/biz/gaspool/base"
	"byd_wallet/model"
	"context"
	"errors"
	"strconv"
	"time"

	"github.com/shopspring/decimal"
	"gorm.io/gorm"
)

type gasPoolHotAccountReader struct {
}

func NewGasPoolHotAccountReader() base.HotAccountReader {
	return &gasPoolHotAccountReader{}
}

func (g *gasPoolHotAccountReader) GetHotAccount(ctx context.Context, chainIndex int64) (privateKey string, err error) {
	panic("unimplemented")
}

type gasPoolUserLocker struct {
	*Data
}

func NewGasPoolUserLocker(data *Data) gaspool.UserLocker {
	return &gasPoolUserLocker{
		Data: data,
	}
}

func (l *gasPoolUserLocker) key(userID uint) string {
	return "gpuserlocker:" + strconv.Itoa(int(userID))
}

func (l *gasPoolUserLocker) TryLock(ctx context.Context, userID uint, expire time.Duration) error {
	return l.rd.SetEx(ctx, l.key(userID), "1", expire).Err()
}

func (l *gasPoolUserLocker) UnLock(ctx context.Context, userID uint) error {
	return l.rd.Del(ctx, l.key(userID)).Err()
}

type gasPoolRepo struct {
	*Data
}

func NewGasPoolRepo(data *Data) *gasPoolRepo {
	return &gasPoolRepo{
		Data: data,
	}
}

func (g *gasPoolRepo) FindGasPoolSponsorTxByID(ctx context.Context, id uint) (*model.GasPoolSponsorTx, error) {
	tx := &model.GasPoolSponsorTx{}
	if err := g.DB(ctx).
		Model(model.GasPoolSponsorTxStub).
		Where("id=?", id).
		Take(tx).Error; err != nil {
		return nil, err
	}
	return tx, nil
}

func (g *gasPoolRepo) DepositGasPool(ctx context.Context, userID uint, amount decimal.Decimal, chainIndex int64, txHash string) (*model.GasPoolFlow, error) {
	if !amount.IsPositive() {
		return nil, errors.New("amount must be positive")
	}

	var flow *model.GasPoolFlow
	err := g.DB(ctx).Transaction(func(tx *gorm.DB) error {
		var gp model.GasPool
		err := tx.Model(model.GasPoolStub).Where("user_id = ?", userID).Take(&gp).Error
		if err != nil {
			return err
		}
		updateBalance := gp.Balance.Add(amount)
		sqlRes := tx.Model(model.GasPoolStub).
			Where("id = ? AND balance = ?", gp.ID, gp.Balance).
			Update("balance", updateBalance)
		if sqlRes.Error != nil {
			return sqlRes.Error
		}
		if sqlRes.RowsAffected != 1 {
			return errors.New("gas pool balance update failed")
		}

		flow = &model.GasPoolFlow{
			FlowType:      model.GasPoolFlowTypeDeposit,
			GasPoolID:     gp.ID,
			UserID:        userID,
			Amount:        amount,
			FlowDirection: model.GasPoolFlowDirectionIn,
			ChainIndex:    chainIndex,
			TxHash:        txHash,
			OldBalance:    gp.Balance,
			Balance:       updateBalance,
		}
		return tx.Create(flow).Error
	})
	return flow, err
}

func (g *gasPoolRepo) ReduceGasPool(ctx context.Context, userID uint, amount decimal.Decimal, chainIndex int64, txHash string) (*model.GasPoolFlow, error) {
	if !amount.IsPositive() {
		return nil, errors.New("amount must be positive")
	}

	var flow *model.GasPoolFlow
	err := g.DB(ctx).Transaction(func(tx *gorm.DB) error {
		var gp model.GasPool
		err := tx.Model(model.GasPoolStub).Where("user_id = ?", userID).Take(&gp).Error
		if err != nil {
			return err
		}
		updateBalance := gp.Balance.Sub(amount)
		if updateBalance.LessThan(decimal.Zero) {
			return errors.New("insufficient balance")
		}
		sqlRes := tx.Model(model.GasPoolStub).
			Where("id = ? AND balance = ?", gp.ID, gp.Balance).
			Update("balance", updateBalance)
		if sqlRes.Error != nil {
			return sqlRes.Error
		}
		if sqlRes.RowsAffected != 1 {
			return errors.New("gas pool balance update failed")
		}

		flow = &model.GasPoolFlow{
			FlowType:      model.GasPoolFlowTypeReduce,
			GasPoolID:     gp.ID,
			UserID:        userID,
			Amount:        amount,
			FlowDirection: model.GasPoolFlowDirectionOut,
			ChainIndex:    chainIndex,
			TxHash:        txHash,
			OldBalance:    gp.Balance,
			Balance:       updateBalance,
		}
		return tx.Create(flow).Error
	})
	return flow, err
}

func (g *gasPoolRepo) RefundGasPool(ctx context.Context, reduceFlowID uint, amount decimal.Decimal) (*model.GasPoolFlow, error) {
	if !amount.IsPositive() {
		return nil, errors.New("amount must be positive")
	}

	var flow *model.GasPoolFlow
	err := g.DB(ctx).Transaction(func(tx *gorm.DB) error {
		var checkId uint
		err := tx.Model(model.GasPoolFlowStub).
			Select("id").
			Where("reduce_flow_id = ?", reduceFlowID).
			Limit(1).
			Scan(&checkId).Error
		if err != nil {
			return err
		}
		if checkId > 0 {
			return errors.New("refund has been processed")
		}

		var reduceFlow model.GasPoolFlow
		err = tx.Model(model.GasPoolFlowStub).Where("id = ?", reduceFlowID).Take(&reduceFlow).Error
		if err != nil {
			return err
		}
		if reduceFlow.Amount.LessThan(amount) {
			return errors.New("refund amount is greater than reduce amount")
		}

		var gp model.GasPool
		err = tx.Model(model.GasPoolStub).Where("user_id = ?", reduceFlow.UserID).Take(&gp).Error
		if err != nil {
			return err
		}
		updateBalance := gp.Balance.Add(amount)
		sqlRes := tx.Model(model.GasPoolStub).
			Where("id = ? AND balance = ?", gp.ID, gp.Balance).
			Update("balance", updateBalance)
		if sqlRes.Error != nil {
			return sqlRes.Error
		}
		if sqlRes.RowsAffected != 1 {
			return errors.New("gas pool balance update failed")
		}

		flow = &model.GasPoolFlow{
			FlowType:      model.GasPoolFlowTypeReduce,
			GasPoolID:     gp.ID,
			UserID:        gp.UserID,
			Amount:        reduceFlow.Amount,
			FlowDirection: model.GasPoolFlowDirectionOut,
			ReduceFlowID:  reduceFlow.ID,
			ChainIndex:    reduceFlow.ChainIndex,
			TxHash:        reduceFlow.TxHash,
			OldBalance:    reduceFlow.OldBalance,
			Balance:       reduceFlow.Balance,
		}
		return tx.Create(flow).Error
	})
	return flow, err
}

func (g *gasPoolRepo) ExistsDepositReceiverAddress(ctx context.Context, chainIndex int64, address string) (bool, error) {
	var id uint
	err := g.DB(ctx).Model(model.GasPoolDepositAddressStub).
		Select("id").
		Where("chain_index = ?", chainIndex).
		Where("address = ?", address).
		Limit(1).
		Scan(&id).Error
	if err != nil {
		return false, err
	}
	return id > 0, nil
}

func (g *gasPoolRepo) FindDepositTokenByAddress(ctx context.Context, chainIndex int64, address string) (*model.GasPoolDepositToken, error) {
	var token model.GasPoolDepositToken
	err := g.DB(ctx).Table(model.GasPoolDepositTokenStub.TableName()+" AS dt").
		Joins("LEFT JOIN token_assets AS ta ON dt.token_asset_id=ta.id").
		Select("ta.*, dt.min_value AS min_value").
		Where("ta.chain_index = ? AND ta.address = ?", chainIndex, address).
		Take(&token).Error
	if err != nil {
		return nil, err
	}
	return &token, nil
}

func (g *gasPoolRepo) FindUserIDByAddress(ctx context.Context, chainIndex int64, address string) (userID uint, err error) {
	err = g.DB(ctx).Model(model.UserAddressStub).
		Select("user_id").
		Where("chain_index = ?", chainIndex).
		Where("address = ?", address).
		Limit(1).
		Scan(&userID).Error
	return
}

func (g *gasPoolRepo) SaveGasPoolSponsorTx(ctx context.Context, tx *model.GasPoolSponsorTx) (*model.GasPoolSponsorTx, error) {
	err := g.DB(ctx).Create(tx).Error
	if err != nil {
		return nil, err
	}
	return tx, err
}

func (g *gasPoolRepo) UpdateGasPoolSponsorTx(ctx context.Context, tx *model.GasPoolSponsorTx) error {
	return g.DB(ctx).Model(model.GasPoolSponsorTxStub).Where("id=?", tx.ID).Updates(tx).Error
}
