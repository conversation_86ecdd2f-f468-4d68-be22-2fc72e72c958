// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: api/wallet/v1/gaspool.proto

package v1

import (
	_ "buf.build/gen/go/bufbuild/protovalidate/protocolbuffers/go/buf/validate"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type DepositToken struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 币名称
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// 币符号
	Symbol string `protobuf:"bytes,2,opt,name=symbol,proto3" json:"symbol,omitempty"`
	// 币精度
	Decimals int64 `protobuf:"varint,3,opt,name=decimals,proto3" json:"decimals,omitempty"`
	// 币图标
	LogoUrl string `protobuf:"bytes,4,opt,name=logo_url,json=logoUrl,proto3" json:"logo_url,omitempty"`
	// 币地址
	Address string `protobuf:"bytes,5,opt,name=address,proto3" json:"address,omitempty"`
	// 链索引
	ChainIndex int64 `protobuf:"varint,6,opt,name=chain_index,json=chainIndex,proto3" json:"chain_index,omitempty"`
	// 链ID
	ChainId string `protobuf:"bytes,7,opt,name=chain_id,json=chainId,proto3" json:"chain_id,omitempty"`
	// 最小充值金额
	MinDepositAmount string `protobuf:"bytes,8,opt,name=min_deposit_amount,json=minDepositAmount,proto3" json:"min_deposit_amount,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *DepositToken) Reset() {
	*x = DepositToken{}
	mi := &file_api_wallet_v1_gaspool_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DepositToken) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DepositToken) ProtoMessage() {}

func (x *DepositToken) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_gaspool_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DepositToken.ProtoReflect.Descriptor instead.
func (*DepositToken) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_gaspool_proto_rawDescGZIP(), []int{0}
}

func (x *DepositToken) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *DepositToken) GetSymbol() string {
	if x != nil {
		return x.Symbol
	}
	return ""
}

func (x *DepositToken) GetDecimals() int64 {
	if x != nil {
		return x.Decimals
	}
	return 0
}

func (x *DepositToken) GetLogoUrl() string {
	if x != nil {
		return x.LogoUrl
	}
	return ""
}

func (x *DepositToken) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

func (x *DepositToken) GetChainIndex() int64 {
	if x != nil {
		return x.ChainIndex
	}
	return 0
}

func (x *DepositToken) GetChainId() string {
	if x != nil {
		return x.ChainId
	}
	return ""
}

func (x *DepositToken) GetMinDepositAmount() string {
	if x != nil {
		return x.MinDepositAmount
	}
	return ""
}

type ListDepositTokenReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListDepositTokenReq) Reset() {
	*x = ListDepositTokenReq{}
	mi := &file_api_wallet_v1_gaspool_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListDepositTokenReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListDepositTokenReq) ProtoMessage() {}

func (x *ListDepositTokenReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_gaspool_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListDepositTokenReq.ProtoReflect.Descriptor instead.
func (*ListDepositTokenReq) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_gaspool_proto_rawDescGZIP(), []int{1}
}

type ListDepositTokenReply struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	List  []*DepositToken        `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	// 余额上限
	BalanceLimit  string `protobuf:"bytes,2,opt,name=balance_limit,json=balanceLimit,proto3" json:"balance_limit,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListDepositTokenReply) Reset() {
	*x = ListDepositTokenReply{}
	mi := &file_api_wallet_v1_gaspool_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListDepositTokenReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListDepositTokenReply) ProtoMessage() {}

func (x *ListDepositTokenReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_gaspool_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListDepositTokenReply.ProtoReflect.Descriptor instead.
func (*ListDepositTokenReply) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_gaspool_proto_rawDescGZIP(), []int{2}
}

func (x *ListDepositTokenReply) GetList() []*DepositToken {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *ListDepositTokenReply) GetBalanceLimit() string {
	if x != nil {
		return x.BalanceLimit
	}
	return ""
}

type GetGasPoolBalanceReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	WalletId      string                 `protobuf:"bytes,1,opt,name=wallet_id,json=walletId,proto3" json:"wallet_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetGasPoolBalanceReq) Reset() {
	*x = GetGasPoolBalanceReq{}
	mi := &file_api_wallet_v1_gaspool_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetGasPoolBalanceReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetGasPoolBalanceReq) ProtoMessage() {}

func (x *GetGasPoolBalanceReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_gaspool_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetGasPoolBalanceReq.ProtoReflect.Descriptor instead.
func (*GetGasPoolBalanceReq) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_gaspool_proto_rawDescGZIP(), []int{3}
}

func (x *GetGasPoolBalanceReq) GetWalletId() string {
	if x != nil {
		return x.WalletId
	}
	return ""
}

type GetGasPoolBalanceReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Balance       string                 `protobuf:"bytes,1,opt,name=balance,proto3" json:"balance,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetGasPoolBalanceReply) Reset() {
	*x = GetGasPoolBalanceReply{}
	mi := &file_api_wallet_v1_gaspool_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetGasPoolBalanceReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetGasPoolBalanceReply) ProtoMessage() {}

func (x *GetGasPoolBalanceReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_gaspool_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetGasPoolBalanceReply.ProtoReflect.Descriptor instead.
func (*GetGasPoolBalanceReply) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_gaspool_proto_rawDescGZIP(), []int{4}
}

func (x *GetGasPoolBalanceReply) GetBalance() string {
	if x != nil {
		return x.Balance
	}
	return ""
}

type GetGasPoolStatReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	WalletId      string                 `protobuf:"bytes,1,opt,name=wallet_id,json=walletId,proto3" json:"wallet_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetGasPoolStatReq) Reset() {
	*x = GetGasPoolStatReq{}
	mi := &file_api_wallet_v1_gaspool_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetGasPoolStatReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetGasPoolStatReq) ProtoMessage() {}

func (x *GetGasPoolStatReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_gaspool_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetGasPoolStatReq.ProtoReflect.Descriptor instead.
func (*GetGasPoolStatReq) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_gaspool_proto_rawDescGZIP(), []int{5}
}

func (x *GetGasPoolStatReq) GetWalletId() string {
	if x != nil {
		return x.WalletId
	}
	return ""
}

type GetGasPoolStatReply struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 充值额度
	TotalDepositAmount string `protobuf:"bytes,1,opt,name=total_deposit_amount,json=totalDepositAmount,proto3" json:"total_deposit_amount,omitempty"`
	// 累计消耗
	TotalReduceAmount string `protobuf:"bytes,2,opt,name=total_reduce_amount,json=totalReduceAmount,proto3" json:"total_reduce_amount,omitempty"`
	// 积分额度 // 暂无
	TotalCreditAmount string `protobuf:"bytes,3,opt,name=total_credit_amount,json=totalCreditAmount,proto3" json:"total_credit_amount,omitempty"`
	// 累计使用次数
	TotalReduceCount int64 `protobuf:"varint,4,opt,name=total_reduce_count,json=totalReduceCount,proto3" json:"total_reduce_count,omitempty"`
	// 支持公链数
	ChainCount    int64 `protobuf:"varint,5,opt,name=chain_count,json=chainCount,proto3" json:"chain_count,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetGasPoolStatReply) Reset() {
	*x = GetGasPoolStatReply{}
	mi := &file_api_wallet_v1_gaspool_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetGasPoolStatReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetGasPoolStatReply) ProtoMessage() {}

func (x *GetGasPoolStatReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_gaspool_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetGasPoolStatReply.ProtoReflect.Descriptor instead.
func (*GetGasPoolStatReply) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_gaspool_proto_rawDescGZIP(), []int{6}
}

func (x *GetGasPoolStatReply) GetTotalDepositAmount() string {
	if x != nil {
		return x.TotalDepositAmount
	}
	return ""
}

func (x *GetGasPoolStatReply) GetTotalReduceAmount() string {
	if x != nil {
		return x.TotalReduceAmount
	}
	return ""
}

func (x *GetGasPoolStatReply) GetTotalCreditAmount() string {
	if x != nil {
		return x.TotalCreditAmount
	}
	return ""
}

func (x *GetGasPoolStatReply) GetTotalReduceCount() int64 {
	if x != nil {
		return x.TotalReduceCount
	}
	return 0
}

func (x *GetGasPoolStatReply) GetChainCount() int64 {
	if x != nil {
		return x.ChainCount
	}
	return 0
}

type ListGasPoolConsumeRecordReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	WalletId      string                 `protobuf:"bytes,1,opt,name=wallet_id,json=walletId,proto3" json:"wallet_id,omitempty"`
	Page          int64                  `protobuf:"varint,2,opt,name=page,proto3" json:"page,omitempty"`
	Limit         int64                  `protobuf:"varint,3,opt,name=limit,proto3" json:"limit,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListGasPoolConsumeRecordReq) Reset() {
	*x = ListGasPoolConsumeRecordReq{}
	mi := &file_api_wallet_v1_gaspool_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListGasPoolConsumeRecordReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListGasPoolConsumeRecordReq) ProtoMessage() {}

func (x *ListGasPoolConsumeRecordReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_gaspool_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListGasPoolConsumeRecordReq.ProtoReflect.Descriptor instead.
func (*ListGasPoolConsumeRecordReq) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_gaspool_proto_rawDescGZIP(), []int{7}
}

func (x *ListGasPoolConsumeRecordReq) GetWalletId() string {
	if x != nil {
		return x.WalletId
	}
	return ""
}

func (x *ListGasPoolConsumeRecordReq) GetPage() int64 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListGasPoolConsumeRecordReq) GetLimit() int64 {
	if x != nil {
		return x.Limit
	}
	return 0
}

type GasPoolConsumeRecord struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 创建时间(时间戳,秒)
	CreatedAt int64 `protobuf:"varint,1,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	// 链索引
	ChainIndex int64 `protobuf:"varint,2,opt,name=chain_index,json=chainIndex,proto3" json:"chain_index,omitempty"`
	// 记录状态(success:执行成功, fail:执行失败, pending:执行中)
	Status string `protobuf:"bytes,3,opt,name=status,proto3" json:"status,omitempty"`
	// 用户交易hash
	TxHash string `protobuf:"bytes,4,opt,name=tx_hash,json=txHash,proto3" json:"tx_hash,omitempty"`
	// GasPool扣款金额(USDT)
	Amount string `protobuf:"bytes,5,opt,name=amount,proto3" json:"amount,omitempty"`
	// 记录类型(transfer:转账, swap:兑换, deposit_without_gas:充值预扣)
	RecordType    string `protobuf:"bytes,6,opt,name=record_type,json=recordType,proto3" json:"record_type,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GasPoolConsumeRecord) Reset() {
	*x = GasPoolConsumeRecord{}
	mi := &file_api_wallet_v1_gaspool_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GasPoolConsumeRecord) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GasPoolConsumeRecord) ProtoMessage() {}

func (x *GasPoolConsumeRecord) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_gaspool_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GasPoolConsumeRecord.ProtoReflect.Descriptor instead.
func (*GasPoolConsumeRecord) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_gaspool_proto_rawDescGZIP(), []int{8}
}

func (x *GasPoolConsumeRecord) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

func (x *GasPoolConsumeRecord) GetChainIndex() int64 {
	if x != nil {
		return x.ChainIndex
	}
	return 0
}

func (x *GasPoolConsumeRecord) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *GasPoolConsumeRecord) GetTxHash() string {
	if x != nil {
		return x.TxHash
	}
	return ""
}

func (x *GasPoolConsumeRecord) GetAmount() string {
	if x != nil {
		return x.Amount
	}
	return ""
}

func (x *GasPoolConsumeRecord) GetRecordType() string {
	if x != nil {
		return x.RecordType
	}
	return ""
}

type ListGasPoolConsumeRecordReply struct {
	state         protoimpl.MessageState  `protogen:"open.v1"`
	TotalCount    int64                   `protobuf:"varint,1,opt,name=total_count,json=totalCount,proto3" json:"total_count,omitempty"`
	List          []*GasPoolConsumeRecord `protobuf:"bytes,2,rep,name=list,proto3" json:"list,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListGasPoolConsumeRecordReply) Reset() {
	*x = ListGasPoolConsumeRecordReply{}
	mi := &file_api_wallet_v1_gaspool_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListGasPoolConsumeRecordReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListGasPoolConsumeRecordReply) ProtoMessage() {}

func (x *ListGasPoolConsumeRecordReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_gaspool_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListGasPoolConsumeRecordReply.ProtoReflect.Descriptor instead.
func (*ListGasPoolConsumeRecordReply) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_gaspool_proto_rawDescGZIP(), []int{9}
}

func (x *ListGasPoolConsumeRecordReply) GetTotalCount() int64 {
	if x != nil {
		return x.TotalCount
	}
	return 0
}

func (x *ListGasPoolConsumeRecordReply) GetList() []*GasPoolConsumeRecord {
	if x != nil {
		return x.List
	}
	return nil
}

type ListGasPoolCashFlowRecordReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	WalletId      string                 `protobuf:"bytes,1,opt,name=wallet_id,json=walletId,proto3" json:"wallet_id,omitempty"`
	Page          int64                  `protobuf:"varint,2,opt,name=page,proto3" json:"page,omitempty"`
	Limit         int64                  `protobuf:"varint,3,opt,name=limit,proto3" json:"limit,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListGasPoolCashFlowRecordReq) Reset() {
	*x = ListGasPoolCashFlowRecordReq{}
	mi := &file_api_wallet_v1_gaspool_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListGasPoolCashFlowRecordReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListGasPoolCashFlowRecordReq) ProtoMessage() {}

func (x *ListGasPoolCashFlowRecordReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_gaspool_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListGasPoolCashFlowRecordReq.ProtoReflect.Descriptor instead.
func (*ListGasPoolCashFlowRecordReq) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_gaspool_proto_rawDescGZIP(), []int{10}
}

func (x *ListGasPoolCashFlowRecordReq) GetWalletId() string {
	if x != nil {
		return x.WalletId
	}
	return ""
}

func (x *ListGasPoolCashFlowRecordReq) GetPage() int64 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListGasPoolCashFlowRecordReq) GetLimit() int64 {
	if x != nil {
		return x.Limit
	}
	return 0
}

type GasPoolCashFlowRecord struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 创建时间(时间戳,秒)
	CreatedAt int64 `protobuf:"varint,1,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	// 链索引
	ChainIndex int64 `protobuf:"varint,2,opt,name=chain_index,json=chainIndex,proto3" json:"chain_index,omitempty"`
	// 币合约地址(矿币为空)
	TokenAddress string `protobuf:"bytes,3,opt,name=token_address,json=tokenAddress,proto3" json:"token_address,omitempty"`
	// 用户交易hash
	TxHash string `protobuf:"bytes,4,opt,name=tx_hash,json=txHash,proto3" json:"tx_hash,omitempty"`
	// GasPool扣款金额(USDT)
	Amount string `protobuf:"bytes,5,opt,name=amount,proto3" json:"amount,omitempty"`
	// 记录类型(deposit:充值, refund:退回)
	RecordType    string `protobuf:"bytes,6,opt,name=record_type,json=recordType,proto3" json:"record_type,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GasPoolCashFlowRecord) Reset() {
	*x = GasPoolCashFlowRecord{}
	mi := &file_api_wallet_v1_gaspool_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GasPoolCashFlowRecord) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GasPoolCashFlowRecord) ProtoMessage() {}

func (x *GasPoolCashFlowRecord) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_gaspool_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GasPoolCashFlowRecord.ProtoReflect.Descriptor instead.
func (*GasPoolCashFlowRecord) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_gaspool_proto_rawDescGZIP(), []int{11}
}

func (x *GasPoolCashFlowRecord) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

func (x *GasPoolCashFlowRecord) GetChainIndex() int64 {
	if x != nil {
		return x.ChainIndex
	}
	return 0
}

func (x *GasPoolCashFlowRecord) GetTokenAddress() string {
	if x != nil {
		return x.TokenAddress
	}
	return ""
}

func (x *GasPoolCashFlowRecord) GetTxHash() string {
	if x != nil {
		return x.TxHash
	}
	return ""
}

func (x *GasPoolCashFlowRecord) GetAmount() string {
	if x != nil {
		return x.Amount
	}
	return ""
}

func (x *GasPoolCashFlowRecord) GetRecordType() string {
	if x != nil {
		return x.RecordType
	}
	return ""
}

type ListGasPoolCashFlowRecordReply struct {
	state         protoimpl.MessageState   `protogen:"open.v1"`
	TotalCount    int64                    `protobuf:"varint,1,opt,name=total_count,json=totalCount,proto3" json:"total_count,omitempty"`
	List          []*GasPoolCashFlowRecord `protobuf:"bytes,2,rep,name=list,proto3" json:"list,omitempty"`
	TokenList     []*DepositToken          `protobuf:"bytes,3,rep,name=token_list,json=tokenList,proto3" json:"token_list,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListGasPoolCashFlowRecordReply) Reset() {
	*x = ListGasPoolCashFlowRecordReply{}
	mi := &file_api_wallet_v1_gaspool_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListGasPoolCashFlowRecordReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListGasPoolCashFlowRecordReply) ProtoMessage() {}

func (x *ListGasPoolCashFlowRecordReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_gaspool_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListGasPoolCashFlowRecordReply.ProtoReflect.Descriptor instead.
func (*ListGasPoolCashFlowRecordReply) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_gaspool_proto_rawDescGZIP(), []int{12}
}

func (x *ListGasPoolCashFlowRecordReply) GetTotalCount() int64 {
	if x != nil {
		return x.TotalCount
	}
	return 0
}

func (x *ListGasPoolCashFlowRecordReply) GetList() []*GasPoolCashFlowRecord {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *ListGasPoolCashFlowRecordReply) GetTokenList() []*DepositToken {
	if x != nil {
		return x.TokenList
	}
	return nil
}

type GetPaymasterReq struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 链索引
	ChainIndex    int64 `protobuf:"varint,1,opt,name=chain_index,json=chainIndex,proto3" json:"chain_index,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetPaymasterReq) Reset() {
	*x = GetPaymasterReq{}
	mi := &file_api_wallet_v1_gaspool_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetPaymasterReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPaymasterReq) ProtoMessage() {}

func (x *GetPaymasterReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_gaspool_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPaymasterReq.ProtoReflect.Descriptor instead.
func (*GetPaymasterReq) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_gaspool_proto_rawDescGZIP(), []int{13}
}

func (x *GetPaymasterReq) GetChainIndex() int64 {
	if x != nil {
		return x.ChainIndex
	}
	return 0
}

type GetPaymasterReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Address       string                 `protobuf:"bytes,1,opt,name=address,proto3" json:"address,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetPaymasterReply) Reset() {
	*x = GetPaymasterReply{}
	mi := &file_api_wallet_v1_gaspool_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetPaymasterReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPaymasterReply) ProtoMessage() {}

func (x *GetPaymasterReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_gaspool_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPaymasterReply.ProtoReflect.Descriptor instead.
func (*GetPaymasterReply) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_gaspool_proto_rawDescGZIP(), []int{14}
}

func (x *GetPaymasterReply) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

var File_api_wallet_v1_gaspool_proto protoreflect.FileDescriptor

const file_api_wallet_v1_gaspool_proto_rawDesc = "" +
	"\n" +
	"\x1bapi/wallet/v1/gaspool.proto\x12\rapi.wallet.v1\x1a\x1cgoogle/api/annotations.proto\x1a\x1bbuf/validate/validate.proto\"\xf5\x01\n" +
	"\fDepositToken\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x12\x16\n" +
	"\x06symbol\x18\x02 \x01(\tR\x06symbol\x12\x1a\n" +
	"\bdecimals\x18\x03 \x01(\x03R\bdecimals\x12\x19\n" +
	"\blogo_url\x18\x04 \x01(\tR\alogoUrl\x12\x18\n" +
	"\aaddress\x18\x05 \x01(\tR\aaddress\x12\x1f\n" +
	"\vchain_index\x18\x06 \x01(\x03R\n" +
	"chainIndex\x12\x19\n" +
	"\bchain_id\x18\a \x01(\tR\achainId\x12,\n" +
	"\x12min_deposit_amount\x18\b \x01(\tR\x10minDepositAmount\"\x15\n" +
	"\x13ListDepositTokenReq\"m\n" +
	"\x15ListDepositTokenReply\x12/\n" +
	"\x04list\x18\x01 \x03(\v2\x1b.api.wallet.v1.DepositTokenR\x04list\x12#\n" +
	"\rbalance_limit\x18\x02 \x01(\tR\fbalanceLimit\"<\n" +
	"\x14GetGasPoolBalanceReq\x12$\n" +
	"\twallet_id\x18\x01 \x01(\tB\a\xbaH\x04r\x02\x10\x01R\bwalletId\"2\n" +
	"\x16GetGasPoolBalanceReply\x12\x18\n" +
	"\abalance\x18\x01 \x01(\tR\abalance\"9\n" +
	"\x11GetGasPoolStatReq\x12$\n" +
	"\twallet_id\x18\x01 \x01(\tB\a\xbaH\x04r\x02\x10\x01R\bwalletId\"\xf6\x01\n" +
	"\x13GetGasPoolStatReply\x120\n" +
	"\x14total_deposit_amount\x18\x01 \x01(\tR\x12totalDepositAmount\x12.\n" +
	"\x13total_reduce_amount\x18\x02 \x01(\tR\x11totalReduceAmount\x12.\n" +
	"\x13total_credit_amount\x18\x03 \x01(\tR\x11totalCreditAmount\x12,\n" +
	"\x12total_reduce_count\x18\x04 \x01(\x03R\x10totalReduceCount\x12\x1f\n" +
	"\vchain_count\x18\x05 \x01(\x03R\n" +
	"chainCount\"\x81\x01\n" +
	"\x1bListGasPoolConsumeRecordReq\x12$\n" +
	"\twallet_id\x18\x01 \x01(\tB\a\xbaH\x04r\x02\x10\x01R\bwalletId\x12\x1b\n" +
	"\x04page\x18\x02 \x01(\x03B\a\xbaH\x04\"\x02 \x00R\x04page\x12\x1f\n" +
	"\x05limit\x18\x03 \x01(\x03B\t\xbaH\x06\"\x04\x18d \x00R\x05limit\"\xc0\x01\n" +
	"\x14GasPoolConsumeRecord\x12\x1d\n" +
	"\n" +
	"created_at\x18\x01 \x01(\x03R\tcreatedAt\x12\x1f\n" +
	"\vchain_index\x18\x02 \x01(\x03R\n" +
	"chainIndex\x12\x16\n" +
	"\x06status\x18\x03 \x01(\tR\x06status\x12\x17\n" +
	"\atx_hash\x18\x04 \x01(\tR\x06txHash\x12\x16\n" +
	"\x06amount\x18\x05 \x01(\tR\x06amount\x12\x1f\n" +
	"\vrecord_type\x18\x06 \x01(\tR\n" +
	"recordType\"y\n" +
	"\x1dListGasPoolConsumeRecordReply\x12\x1f\n" +
	"\vtotal_count\x18\x01 \x01(\x03R\n" +
	"totalCount\x127\n" +
	"\x04list\x18\x02 \x03(\v2#.api.wallet.v1.GasPoolConsumeRecordR\x04list\"\x82\x01\n" +
	"\x1cListGasPoolCashFlowRecordReq\x12$\n" +
	"\twallet_id\x18\x01 \x01(\tB\a\xbaH\x04r\x02\x10\x01R\bwalletId\x12\x1b\n" +
	"\x04page\x18\x02 \x01(\x03B\a\xbaH\x04\"\x02 \x00R\x04page\x12\x1f\n" +
	"\x05limit\x18\x03 \x01(\x03B\t\xbaH\x06\"\x04\x18d \x00R\x05limit\"\xce\x01\n" +
	"\x15GasPoolCashFlowRecord\x12\x1d\n" +
	"\n" +
	"created_at\x18\x01 \x01(\x03R\tcreatedAt\x12\x1f\n" +
	"\vchain_index\x18\x02 \x01(\x03R\n" +
	"chainIndex\x12#\n" +
	"\rtoken_address\x18\x03 \x01(\tR\ftokenAddress\x12\x17\n" +
	"\atx_hash\x18\x04 \x01(\tR\x06txHash\x12\x16\n" +
	"\x06amount\x18\x05 \x01(\tR\x06amount\x12\x1f\n" +
	"\vrecord_type\x18\x06 \x01(\tR\n" +
	"recordType\"\xb7\x01\n" +
	"\x1eListGasPoolCashFlowRecordReply\x12\x1f\n" +
	"\vtotal_count\x18\x01 \x01(\x03R\n" +
	"totalCount\x128\n" +
	"\x04list\x18\x02 \x03(\v2$.api.wallet.v1.GasPoolCashFlowRecordR\x04list\x12:\n" +
	"\n" +
	"token_list\x18\x03 \x03(\v2\x1b.api.wallet.v1.DepositTokenR\ttokenList\"2\n" +
	"\x0fGetPaymasterReq\x12\x1f\n" +
	"\vchain_index\x18\x01 \x01(\x03R\n" +
	"chainIndex\"-\n" +
	"\x11GetPaymasterReply\x12\x18\n" +
	"\aaddress\x18\x01 \x01(\tR\aaddress2\xad\x06\n" +
	"\n" +
	"GasPoolSrv\x12\x80\x01\n" +
	"\x10ListDepositToken\x12\".api.wallet.v1.ListDepositTokenReq\x1a$.api.wallet.v1.ListDepositTokenReply\"\"\x82\xd3\xe4\x93\x02\x1c\x12\x1a/v1/gaspool/deposit_tokens\x12|\n" +
	"\x11GetGasPoolBalance\x12#.api.wallet.v1.GetGasPoolBalanceReq\x1a%.api.wallet.v1.GetGasPoolBalanceReply\"\x1b\x82\xd3\xe4\x93\x02\x15\x12\x13/v1/gaspool/balance\x12p\n" +
	"\x0eGetGasPoolStat\x12 .api.wallet.v1.GetGasPoolStatReq\x1a\".api.wallet.v1.GetGasPoolStatReply\"\x18\x82\xd3\xe4\x93\x02\x12\x12\x10/v1/gaspool/stat\x12\x99\x01\n" +
	"\x18ListGasPoolConsumeRecord\x12*.api.wallet.v1.ListGasPoolConsumeRecordReq\x1a,.api.wallet.v1.ListGasPoolConsumeRecordReply\"#\x82\xd3\xe4\x93\x02\x1d\x12\x1b/v1/gaspool/consume_records\x12\x9e\x01\n" +
	"\x19ListGasPoolCashFlowRecord\x12+.api.wallet.v1.ListGasPoolCashFlowRecordReq\x1a-.api.wallet.v1.ListGasPoolCashFlowRecordReply\"%\x82\xd3\xe4\x93\x02\x1f\x12\x1d/v1/gaspool/cash_flow_records\x12o\n" +
	"\fGetPaymaster\x12\x1e.api.wallet.v1.GetPaymasterReq\x1a .api.wallet.v1.GetPaymasterReply\"\x1d\x82\xd3\xe4\x93\x02\x17\x12\x15/v1/gaspool/paymasterB\x94\x01\n" +
	"\x11com.api.wallet.v1B\fGaspoolProtoP\x01Z\x1bbyd_wallet/api/wallet/v1;v1\xa2\x02\x03AWX\xaa\x02\rApi.Wallet.V1\xca\x02\rApi\\Wallet\\V1\xe2\x02\x19Api\\Wallet\\V1\\GPBMetadata\xea\x02\x0fApi::Wallet::V1b\x06proto3"

var (
	file_api_wallet_v1_gaspool_proto_rawDescOnce sync.Once
	file_api_wallet_v1_gaspool_proto_rawDescData []byte
)

func file_api_wallet_v1_gaspool_proto_rawDescGZIP() []byte {
	file_api_wallet_v1_gaspool_proto_rawDescOnce.Do(func() {
		file_api_wallet_v1_gaspool_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_api_wallet_v1_gaspool_proto_rawDesc), len(file_api_wallet_v1_gaspool_proto_rawDesc)))
	})
	return file_api_wallet_v1_gaspool_proto_rawDescData
}

var file_api_wallet_v1_gaspool_proto_msgTypes = make([]protoimpl.MessageInfo, 15)
var file_api_wallet_v1_gaspool_proto_goTypes = []any{
	(*DepositToken)(nil),                   // 0: api.wallet.v1.DepositToken
	(*ListDepositTokenReq)(nil),            // 1: api.wallet.v1.ListDepositTokenReq
	(*ListDepositTokenReply)(nil),          // 2: api.wallet.v1.ListDepositTokenReply
	(*GetGasPoolBalanceReq)(nil),           // 3: api.wallet.v1.GetGasPoolBalanceReq
	(*GetGasPoolBalanceReply)(nil),         // 4: api.wallet.v1.GetGasPoolBalanceReply
	(*GetGasPoolStatReq)(nil),              // 5: api.wallet.v1.GetGasPoolStatReq
	(*GetGasPoolStatReply)(nil),            // 6: api.wallet.v1.GetGasPoolStatReply
	(*ListGasPoolConsumeRecordReq)(nil),    // 7: api.wallet.v1.ListGasPoolConsumeRecordReq
	(*GasPoolConsumeRecord)(nil),           // 8: api.wallet.v1.GasPoolConsumeRecord
	(*ListGasPoolConsumeRecordReply)(nil),  // 9: api.wallet.v1.ListGasPoolConsumeRecordReply
	(*ListGasPoolCashFlowRecordReq)(nil),   // 10: api.wallet.v1.ListGasPoolCashFlowRecordReq
	(*GasPoolCashFlowRecord)(nil),          // 11: api.wallet.v1.GasPoolCashFlowRecord
	(*ListGasPoolCashFlowRecordReply)(nil), // 12: api.wallet.v1.ListGasPoolCashFlowRecordReply
	(*GetPaymasterReq)(nil),                // 13: api.wallet.v1.GetPaymasterReq
	(*GetPaymasterReply)(nil),              // 14: api.wallet.v1.GetPaymasterReply
}
var file_api_wallet_v1_gaspool_proto_depIdxs = []int32{
	0,  // 0: api.wallet.v1.ListDepositTokenReply.list:type_name -> api.wallet.v1.DepositToken
	8,  // 1: api.wallet.v1.ListGasPoolConsumeRecordReply.list:type_name -> api.wallet.v1.GasPoolConsumeRecord
	11, // 2: api.wallet.v1.ListGasPoolCashFlowRecordReply.list:type_name -> api.wallet.v1.GasPoolCashFlowRecord
	0,  // 3: api.wallet.v1.ListGasPoolCashFlowRecordReply.token_list:type_name -> api.wallet.v1.DepositToken
	1,  // 4: api.wallet.v1.GasPoolSrv.ListDepositToken:input_type -> api.wallet.v1.ListDepositTokenReq
	3,  // 5: api.wallet.v1.GasPoolSrv.GetGasPoolBalance:input_type -> api.wallet.v1.GetGasPoolBalanceReq
	5,  // 6: api.wallet.v1.GasPoolSrv.GetGasPoolStat:input_type -> api.wallet.v1.GetGasPoolStatReq
	7,  // 7: api.wallet.v1.GasPoolSrv.ListGasPoolConsumeRecord:input_type -> api.wallet.v1.ListGasPoolConsumeRecordReq
	10, // 8: api.wallet.v1.GasPoolSrv.ListGasPoolCashFlowRecord:input_type -> api.wallet.v1.ListGasPoolCashFlowRecordReq
	13, // 9: api.wallet.v1.GasPoolSrv.GetPaymaster:input_type -> api.wallet.v1.GetPaymasterReq
	2,  // 10: api.wallet.v1.GasPoolSrv.ListDepositToken:output_type -> api.wallet.v1.ListDepositTokenReply
	4,  // 11: api.wallet.v1.GasPoolSrv.GetGasPoolBalance:output_type -> api.wallet.v1.GetGasPoolBalanceReply
	6,  // 12: api.wallet.v1.GasPoolSrv.GetGasPoolStat:output_type -> api.wallet.v1.GetGasPoolStatReply
	9,  // 13: api.wallet.v1.GasPoolSrv.ListGasPoolConsumeRecord:output_type -> api.wallet.v1.ListGasPoolConsumeRecordReply
	12, // 14: api.wallet.v1.GasPoolSrv.ListGasPoolCashFlowRecord:output_type -> api.wallet.v1.ListGasPoolCashFlowRecordReply
	14, // 15: api.wallet.v1.GasPoolSrv.GetPaymaster:output_type -> api.wallet.v1.GetPaymasterReply
	10, // [10:16] is the sub-list for method output_type
	4,  // [4:10] is the sub-list for method input_type
	4,  // [4:4] is the sub-list for extension type_name
	4,  // [4:4] is the sub-list for extension extendee
	0,  // [0:4] is the sub-list for field type_name
}

func init() { file_api_wallet_v1_gaspool_proto_init() }
func file_api_wallet_v1_gaspool_proto_init() {
	if File_api_wallet_v1_gaspool_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_api_wallet_v1_gaspool_proto_rawDesc), len(file_api_wallet_v1_gaspool_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   15,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_wallet_v1_gaspool_proto_goTypes,
		DependencyIndexes: file_api_wallet_v1_gaspool_proto_depIdxs,
		MessageInfos:      file_api_wallet_v1_gaspool_proto_msgTypes,
	}.Build()
	File_api_wallet_v1_gaspool_proto = out.File
	file_api_wallet_v1_gaspool_proto_goTypes = nil
	file_api_wallet_v1_gaspool_proto_depIdxs = nil
}
