package swap

import (
	"byd_wallet/common"
	"byd_wallet/common/constant"
	"byd_wallet/internal/data"
	"byd_wallet/model"
	"context"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/urfave/cli/v3"
)

var Cmd = &cli.Command{
	Name:  "swap",
	Usage: "兑换数据初始化",
	Flags: []cli.Flag{
		&cli.StringFlag{
			Name:     "conf",
			Usage:    "数据库配置文件路径",
			Required: true,
		},
		&cli.BoolFlag{
			Name:  "test",
			Usage: "初始化测试数据(只用于测试)",
			Value: false,
		},
		&cli.BoolFlag{
			Name:  "dryrun",
			Usage: "检查数据是否正确",
			Value: false,
		},
		&cli.StringSliceFlag{
			Name: "hash",
		},
	},
	Action: run,
}

func run(ctx context.Context, cmd *cli.Command) error {
	bc, err := common.NewBootstrapConfig(cmd.String("conf"))
	if err != nil {
		return err
	}
	db, cleanup, err := data.NewGormDB(bc.Data)
	if err != nil {
		return err
	}
	defer cleanup()

	uc, cf, err := wireUsecase(bc.Data, log.DefaultLogger)
	if err != nil {
		return err
	}
	defer cf()

	if cmd.Bool("test") {
		// 临时添加热门代币
		var count int64
		if err := db.WithContext(ctx).Model(&model.SwappableHotToken{}).Count(&count).Error; err != nil {
			return err
		}
		if count == 0 {
			var tokens []*model.SwappableToken
			if err := db.WithContext(ctx).Model(&model.SwappableToken{}).Find(&tokens).Error; err != nil {
				return err
			}
			var hotTokens []*model.SwappableHotToken
			for i, token := range tokens {
				hotTokens = append(hotTokens, &model.SwappableHotToken{
					SwappableTokenID: token.ID,
					ChainIndex:       token.ChainIndex,
					SortOrder:        i,
				})
				hotTokens = append(hotTokens, &model.SwappableHotToken{
					SwappableTokenID: token.ID,
					ChainIndex:       token.ChainIndex,
					SortOrder:        i,
					IsAll:            true,
				})
			}
			if err := db.WithContext(ctx).Create(hotTokens).Error; err != nil {
				return err
			}
		}

		if err := uc.CreateSwapRecordsByHashes(ctx, cmd.StringSlice("hash")); err != nil {
			return err
		}
		return nil
	}

	channelName := "metapath"
	if err := db.WithContext(ctx).FirstOrCreate(&model.APIConfig{
		API:    channelName,
		Config: []byte(`{"source": "BossWallet", "base_url": "https://api-swap.paths.finance"}`),
	}, "api=?", channelName).Error; err != nil {
		return err
	}
	channel := &model.SwapChannel{
		Name:   model.SwapChannelNameMetapath,
		Enable: true,
	}
	if err := db.WithContext(ctx).FirstOrCreate(channel, "name=?", channelName).Error; err != nil {
		return err
	}
	var chainCount int64
	if err := db.WithContext(ctx).Model(&model.SwappableBlockchainNetwork{}).Count(&chainCount).Error; err != nil {
		return err
	}
	if chainCount == 0 {
		var chains []*model.BlockchainNetwork
		if err := db.WithContext(ctx).Model(&model.BlockchainNetwork{}).Where("chain_index <> ?", constant.BtcChainIndex).Find(&chains).Error; err != nil {
			return err
		}
		var swappableChains []*model.SwappableBlockchainNetwork
		for _, chain := range chains {
			swappableChains = append(swappableChains, &model.SwappableBlockchainNetwork{
				SwapChannelID:       channel.ID,
				BlockchainNetworkID: chain.ID,
			})
		}
		if err := db.WithContext(ctx).Create(&swappableChains).Error; err != nil {
			return err
		}
	}
	var tokenCount int64
	if err = db.WithContext(ctx).Model(&model.SwappableToken{}).Count(&tokenCount).Error; err != nil {
		return err
	}
	if tokenCount == 0 {
		if err := uc.RefreshSwappableTokens(ctx, cmd.Bool("dryrun")); err != nil {
			return err
		}
	}

	if err = db.WithContext(ctx).Model(&model.SwapConfig{}).FirstOrCreate(&model.SwapConfig{
		SwapChannelID:        channel.ID,
		CronSpec:             "@every 10s",
		RecordMaxConcurrency: 100,
	}, "swap_channel_id=?", channel.ID).Error; err != nil {
		return err
	}

	return nil
}
