package solana

import (
	"context"
	"errors"
	"fmt"

	"github.com/gagliardetto/solana-go/rpc"
	"github.com/shopspring/decimal"
)

// fee.go - Solana paymaster 费用计算相关功能
// 包含交易费用获取和计算等功能

// GetTransactionFeeWithContext 获取交易费用（带上下文）
// 参数：
//   - ctx: 上下文对象
//   - message: 交易消息字符串
//
// 返回：
//   - decimal.Decimal: 交易费用（以lamports为单位）
//   - error: 获取过程中的错误
func (pm *Paymaster) GetTransactionFeeWithContext(ctx context.Context, message string) (decimal.Decimal, error) {
	if message == "" {
		return decimal.Decimal{}, fmt.Errorf("message is empty")
	}

	pm.log.Debugf("开始获取交易费用，消息长度: %d", len(message))

	feeResp, err := pm.solCli.Select().GetFeeForMessage(ctx, message, rpc.CommitmentFinalized)
	if err != nil {
		return decimal.Decimal{}, fmt.Errorf("get fee error: %w", err)
	}
	if feeResp == nil {
		return decimal.Decimal{}, errors.New("rpc response is nil")
	}
	if feeResp.Value == nil {
		return decimal.Decimal{}, errors.New("fee response is nil")
	}

	fee := decimal.NewFromInt(int64(*feeResp.Value))
	return fee, nil
}
