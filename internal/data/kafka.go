package data

import (
	"byd_wallet/internal/biz"
	"byd_wallet/internal/conf"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"strconv"
	"time"

	"github.com/segmentio/kafka-go"
)

type txSyncEventPublisher struct {
	tokenWriter        *kafka.Writer
	holdNewTokenWriter *kafka.Writer
}

func NewTxSyncEventPublisher(c *conf.Data) (biz.TxSyncEventPublisher, func(), error) {
	tokenWriter, cleanup, err := newKafkaWriter(c.Kafka.Brokers, c.Kafka.TokenTopic)
	if err != nil {
		return nil, nil, err
	}
	holdNewTokenWriter, cleanup2, err := newKafkaWriter(c.Kaf<PERSON>.Broke<PERSON>, c.Kafka.HoldNewTokenTopic)
	if err != nil {
		cleanup()
		return nil, nil, err
	}
	return &txSyncEventPublisher{
			tokenWriter:        tokenWriter,
			holdNewTokenWriter: holdNewTokenWriter,
		}, func() {
			cleanup()
			cleanup2()
		}, nil
}

func (t *txSyncEventPublisher) Publish(ctx context.Context, event interface{}) error {
	var w *kafka.Writer
	switch event.(type) {
	case *biz.TxSyncEventTokenDeploy:
		w = t.tokenWriter
	case *biz.TxSyncEventHoldNewToken:
		w = t.holdNewTokenWriter
	default:
		return fmt.Errorf("unknown event type: %v", event)
	}

	ejson, err := json.Marshal(event)
	if err != nil {
		return err
	}
	message := kafka.Message{
		Key:   []byte(strconv.FormatInt(time.Now().Unix(), 10)),
		Value: ejson,
	}

	return w.WriteMessages(ctx, message)
}

func newKafkaWriter(brokers []string, topic string) (*kafka.Writer, func(), error) {
	if len(brokers) == 0 {
		return nil, nil, errors.New("no kafka brokers provided")
	}
	w := &kafka.Writer{
		Addr:     kafka.TCP(brokers...),
		Topic:    topic,
		Balancer: &kafka.LeastBytes{},
	}
	cleanup := func() {
		_ = w.Close()
	}
	if err := createTopicIfNotExists(brokers[0], topic); err != nil {
		cleanup()
		return nil, nil, fmt.Errorf("createTopicIfNotExists: %w", err)
	}
	return w, cleanup, nil
}

func createTopicIfNotExists(brokerAddr, topic string) error {
	conn, err := kafka.Dial("tcp", brokerAddr)
	if err != nil {
		return err
	}
	defer conn.Close()

	ctrl, err := conn.Controller() // must be controller
	if err != nil {
		return err
	}

	ctrlConn, err := kafka.Dial("tcp", fmt.Sprintf("%s:%d", ctrl.Host, ctrl.Port))
	if err != nil {
		return err
	}
	defer ctrlConn.Close()

	topicConfigs := []kafka.TopicConfig{
		{
			Topic:             topic,
			NumPartitions:     3,
			ReplicationFactor: 1,
		},
	}

	// CreateTopics creates one topic per provided configuration with idempotent
	// operational semantics. In other words, if CreateTopics is invoked with a
	// configuration for an existing topic, it will have no effect.

	// CreateTopics = CreateTopicIfNotExists
	return ctrlConn.CreateTopics(topicConfigs...)
}
