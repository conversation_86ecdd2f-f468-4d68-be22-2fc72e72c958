package bsc

import (
	"byd_wallet/common/constant"
	"byd_wallet/internal/biz/gaspool"
	"byd_wallet/utils"
	"context"
	"fmt"
	"math/big"

	"github.com/ethereum/go-ethereum"
	"github.com/ethereum/go-ethereum/core/types"
	"github.com/shopspring/decimal"
)

func (pm *Paymaster) estimateGasTransferFee(ctx context.Context, tx *gaspool.UserTx) (*gaspool.UserTxGas, error) {
	// 步骤1: 解码EVM交易
	evmTx, err := utils.RlpDecodeBytes(tx.RawTxHex)
	if err != nil {
		return nil, fmt.Errorf("解码EVM交易失败: %w", err)
	}

	// 步骤2: 获取BSC客户端
	client, err := pm.evmCli.Select(pm.chainIndex)
	if err != nil {
		return nil, fmt.Errorf("获取BSC客户端失败: %w", err)
	}

	// 步骤3: 获取原始gas价格并保存用于条件判断
	originalUserGasPrice := evmTx.GasPrice()
	userGasPrice := originalUserGasPrice

	// 记录原始gas price是否为0，用于后续判断是否需要gas转账
	// 当原始gas price为0时，表示使用MegaFuel服务，无需从热钱包转账gas
	isZeroGasPrice := originalUserGasPrice == nil || originalUserGasPrice.Sign() == 0

	if isZeroGasPrice {
		// 如果用户交易没有设置gas价格，获取当前网络建议价格用于计算
		userGasPrice, err = client.SuggestGasPrice(ctx)
		if err != nil {
			pm.log.Warnf("获取建议gas价格失败，使用默认值: %v", err)
			userGasPrice = big.NewInt(constant.DefaultBaseFee + constant.DefaultGasTipCap)
		}
		pm.log.Debugf("BSC链检测到零gas价格交易，将使用MegaFuel服务，无需gas转账")
	}

	// 步骤4: 应用1.2倍安全系数到gas价格
	// 使用decimal进行精确的浮点数计算，避免类型转换错误
	safetyFactor := decimal.NewFromFloat(DefaultGasLimitMultiplier) // 1.2
	originalGasPriceDecimal := decimal.NewFromBigInt(userGasPrice, 0)
	adjustedGasPrice := originalGasPriceDecimal.Mul(safetyFactor)
	userGasPrice = adjustedGasPrice.BigInt()

	// 步骤5: 计算用户交易gas费用
	userGasLimit := big.NewInt(int64(evmTx.Gas()))
	userGasWei := new(big.Int).Mul(userGasLimit, userGasPrice)

	// 步骤6: 根据原始gas price条件性估算gas转账费用
	var transferGasFee *big.Int
	var totalGasWei *big.Int

	if isZeroGasPrice {
		// 原始gas price为0：使用MegaFuel服务，无需从热钱包转账gas
		// 总费用仅包含用户交易费用
		transferGasFee = big.NewInt(0)
		totalGasWei = new(big.Int).Set(userGasWei)
		pm.log.Debugf("BSC链零gas价格模式 - 用户交易: %s wei, gas转账: %s wei, 总计: %s wei",
			userGasWei.String(), transferGasFee.String(), totalGasWei.String())
	} else {
		// 原始gas price大于0：需要从热钱包向用户转账gas
		// 估算gas转账费用并添加到总费用中
		var err error
		transferGasFee, err = pm.estimateGasTransferCost(ctx, evmTx, userGasPrice)
		if err != nil {
			pm.log.Warnf("估算gas转账费用失败，使用默认值: %v", err)
			// 使用默认的gas转账费用（21000 gas limit * gas price）
			defaultTransferGasLimit := big.NewInt(21000)
			transferGasFee = new(big.Int).Mul(defaultTransferGasLimit, userGasPrice)
		}

		// 计算总gas费用（用户交易 + gas转账）
		totalGasWei = new(big.Int).Add(userGasWei, transferGasFee)
		pm.log.Debugf("BSC链标准模式 - 用户交易: %s wei, gas转账: %s wei, 总计: %s wei",
			userGasWei.String(), transferGasFee.String(), totalGasWei.String())
	}

	// 步骤8: 获取BNB价格
	price, timeUnix, err := pm.tokenPriceReader.GetTokenLatestPriceUSDT(ctx, pm.chainIndex, "")
	if err != nil {
		return nil, fmt.Errorf("获取BNB价格失败: %w", err)
	}
	// 检查价格是否过期
	if pm.isPriceTimeExpired(timeUnix) {
		return nil, fmt.Errorf("BNB价格已过期: %d", timeUnix)
	}

	pm.log.Debugf("BNB价格: %s USDT", price.String())

	// 步骤9: 计算gas费用对应的USDT金额
	// BNB使用18位精度，与ETH相同
	baseUnit := constant.BaseUnitPerETH
	totalGasDecimal := decimal.NewFromBigInt(totalGasWei, 0)
	gasInBNB := totalGasDecimal.Div(baseUnit)
	gasUSDT := gasInBNB.Mul(price)

	// 转换为6位精度的USDT（标准USDT精度）
	gasUSDTFormatted := gasUSDT.Mul(decimal.NewFromInt(1000000)).Truncate(0)

	pm.log.Debugf("BSC链gas费用转换: %s wei = %s BNB = %s USDT (原始) = %s USDT (6位精度)",
		totalGasWei.String(), gasInBNB.String(), gasUSDT.String(), gasUSDTFormatted.String())

	return &gaspool.UserTxGas{
		Gas:           totalGasDecimal,
		GasUSDT:       gasUSDTFormatted,
		Price:         price,
		PriceTimeUnix: timeUnix,
	}, nil
}

// estimateGasTransferCost 估算从热钱包向用户转账gas的费用
// 参数:
//   - ctx: 上下文对象
//   - userTx: 用户的原始交易，用于获取发送者地址
//   - gasPrice: 当前gas价格
//
// 返回值:
//   - *big.Int: 估算的gas转账费用（以wei为单位）
//   - error: 错误信息
func (pm *Paymaster) estimateGasTransferCost(ctx context.Context, userTx *types.Transaction, gasPrice *big.Int) (*big.Int, error) {
	pm.log.Debugf("开始估算BSC链gas转账费用")

	// 步骤1: 获取交易发送者地址
	senderAddr, err := utils.GetTxSender(userTx)
	if err != nil {
		return nil, fmt.Errorf("获取交易发送者地址失败: %w", err)
	}

	// 步骤2: 获取BSC客户端
	client, err := pm.evmCli.Select(pm.chainIndex)
	if err != nil {
		return nil, fmt.Errorf("获取BSC客户端失败: %w", err)
	}

	// 步骤3: 获取热钱包私钥和地址
	hotWalletPrivateKey, err := pm.hotAccountReader.GetHotAccount(ctx, pm.chainIndex)
	if err != nil {
		return nil, fmt.Errorf("获取热钱包私钥失败: %w", err)
	}

	hotWalletAddr, _, err := utils.GetAddressByPrivateKey(hotWalletPrivateKey)
	if err != nil {
		return nil, fmt.Errorf("解析热钱包地址失败: %w", err)
	}

	// 步骤4: 使用EstimateGas进行精确估算
	// 模拟从热钱包向用户地址转账少量BNB的gas费用
	simulationValue := big.NewInt(1) // 1 wei用于模拟
	estimatedGasLimit, err := client.EstimateGas(ctx, ethereum.CallMsg{
		From:  hotWalletAddr,
		To:    &senderAddr,
		Value: simulationValue,
		Data:  nil,
	})
	if err != nil {
		return nil, fmt.Errorf("EstimateGas失败: %w", err)
	}

	// 步骤5: 应用安全系数（1.2倍）
	safetyFactor := decimal.NewFromFloat(DefaultGasLimitMultiplier)
	safeGasLimit := decimal.NewFromInt(int64(estimatedGasLimit)).Mul(safetyFactor)
	finalGasLimit := safeGasLimit.BigInt()

	// 步骤6: 计算总gas转账费用
	totalTransferFee := new(big.Int).Mul(finalGasLimit, gasPrice)

	pm.log.Debugf("BSC链gas转账费用估算成功 - 估算GasLimit: %d, 安全GasLimit: %s, GasPrice: %s wei, 总费用: %s wei",
		estimatedGasLimit, finalGasLimit.String(), gasPrice.String(), totalTransferFee.String())

	return totalTransferFee, nil
}
