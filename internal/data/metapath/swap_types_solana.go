package metapath

import "encoding/json"

type SolanaSwapResultData struct {
	TxData *SolanaSwapTxData `json:"txData"`
}

type SolanaSwapTxData struct {
	TransferData *TransferData  `json:"transferData"`
	Tx           []SolanaTxData `json:"tx"`
	SolanaTx     SolanaTx       `json:"solanaTx"`
	//Signer       interface{}    `json:"signer"`
}

func (s SolanaSwapTxData) GetTransferData() *TransferData {
	return s.TransferData
}

func (s SolanaSwapTxData) IsEmpty() bool {
	return s.SolanaTx.SerializedMessage == ""
}

type SolanaTx struct {
	SerializedMessage string `json:"serializedMessage"`
	From              string `json:"from"`
}

type SolanaTxData struct {
	//Data []int `json:"data"`
	//Keys []struct {
	//	IsSigner   bool   `json:"isSigner"`
	//	IsWritable bool   `json:"isWritable"`
	//	Pubkey     string `json:"pubkey"`
	//} `json:"keys"`
	ProgramId string `json:"programId"`
}

func (r *SwapResponse) GetSolanaSwapTxData() (*SolanaSwapTxData, error) {
	var result SolanaSwapResultData
	err := json.Unmarshal(r.Data, &result)
	return result.TxData, err
}
