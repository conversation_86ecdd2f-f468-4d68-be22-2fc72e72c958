package data

import (
	"byd_wallet/common/lang"
	"byd_wallet/internal/biz"
	"byd_wallet/model"
	"context"
	"errors"
	"fmt"
	"gorm.io/gorm"
	"slices"
	"sort"
)

func NewApprovalRepo(data *Data) biz.ApprovalRepo {
	return &approvalRepo{data}
}

type approvalRepo struct {
	*Data
}

func (a approvalRepo) ListApprovedAddresses(ctx context.Context, addresses []model.UserAddress) ([]model.UserAddress, error) {
	var whereIns [][]any
	for _, address := range addresses {
		whereIns = append(whereIns, []any{address.Address, address.ChainIndex})
	}
	var approvals []*model.Approval
	if err := a.DB(ctx).Model(&model.Approval{}).
		Where("(owner_address, chain_index) IN ?", whereIns).
		Where("value > ?", 0).
		Find(&approvals).Error; err != nil {
		return nil, err
	}
	var result []model.UserAddress
	exists := make(map[string]bool)
	for _, approval := range approvals {
		key := fmt.Sprintf("%d-%s", approval.ChainIndex, approval.OwnerAddress)
		_, ok := exists[key]
		if ok {
			continue
		}
		exists[key] = true
		result = append(result, model.UserAddress{
			ChainIndex: approval.ChainIndex,
			Address:    approval.OwnerAddress,
		})
	}
	return result, nil
}

func (a approvalRepo) ListApprovalByUserAddress(ctx context.Context, address model.UserAddress) (*biz.TokenDappApprovals, error) {
	var approvals []*model.Approval
	if err := a.DB(ctx).Model(&model.Approval{}).
		Where("owner_address = ?", address.Address).
		Where("chain_index = ?", address.ChainIndex).
		Where("value > ?", 0).
		Find(&approvals).Error; err != nil {
		return nil, err
	}
	var network model.BlockchainNetwork
	if err := a.DB(ctx).Model(&model.BlockchainNetwork{}).Where("chain_index = ?", address.ChainIndex).Take(&network).Error; err != nil {
		return nil, err
	}
	out := &biz.TokenDappApprovals{
		Address: address,
		Network: &network,
	}
	language := lang.FromContext(ctx)
	for _, approval := range approvals {
		tokenDappApproval := &biz.TokenDappApproval{
			Token: &model.TokenAsset{
				ChainIndex: approval.ChainIndex,
				Address:    approval.TokenAddress,
			},
			Approval: approval,
		}
		var dappNetwork model.DappBlockchainNetwork
		if err := a.DB(ctx).Model(&model.DappBlockchainNetwork{}).
			Where("blockchain_network_id = ?", network.ID).
			Where("address = ?", approval.SpenderAddress).
			Take(&dappNetwork).Error; err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, err
		}
		if dappNetwork.DappID > 0 {
			var dapp model.Dapp
			if err := a.DB(ctx).Model(&model.Dapp{}).
				Preload("DappI18Ns", "language=?", language).
				Take(&dapp, dappNetwork.DappID).Error; err != nil {
				return nil, err
			}
			tokenDappApproval.Dapp = &dapp
		}
		var token model.TokenAsset
		if err := a.DB(ctx).Model(&model.TokenAsset{}).
			Where("chain_index = ?", approval.ChainIndex).
			Where("address = ?", approval.TokenAddress).
			Take(&token).Error; err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, err
		}
		if token.ID > 0 {
			tokenDappApproval.Token = &token
		}
		out.TokenDappApproval = append(out.TokenDappApproval, tokenDappApproval)
	}
	return out, nil
}

func (a approvalRepo) ListApprovedDappsByUserAddresses(ctx context.Context, addresses []model.UserAddress) ([]*biz.UserApprovedDapp, error) {
	var whereIns [][]any
	for _, address := range addresses {
		whereIns = append(whereIns, []any{address.Address, address.ChainIndex})
	}

	var approvals []*model.Approval
	if err := a.DB(ctx).Model(&model.Approval{}).
		Where("(owner_address, chain_index) IN ?", whereIns).
		Where("value > ?", 0).
		Find(&approvals).Error; err != nil {
		return nil, err
	}

	language := lang.FromContext(ctx)
	outMap := make(map[string]*biz.UserApprovedDapp)
	for _, approval := range approvals {
		key := fmt.Sprintf("%d-%s", approval.ChainIndex, approval.SpenderAddress)
		data, ok := outMap[key]
		if ok {
			if slices.Contains(data.Addresses, approval.OwnerAddress) {
				continue
			}
			data.Addresses = append(data.Addresses, approval.OwnerAddress)
			continue
		}
		var network model.BlockchainNetwork
		if err := a.DB(ctx).Model(&model.BlockchainNetwork{}).Where("chain_index = ?", approval.ChainIndex).Take(&network).Error; err != nil {
			return nil, err
		}
		var dappNetwork model.DappBlockchainNetwork
		if err := a.DB(ctx).Model(&model.DappBlockchainNetwork{}).
			Where("address = ?", approval.SpenderAddress).
			Where("blockchain_network_id = ?", network.ID).
			Preload("Dapp").
			Preload("Dapp.DappI18Ns", "language=?", language).
			Take(&dappNetwork).Error; err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, err
		}
		outMap[key] = &biz.UserApprovedDapp{
			Addresses:         []string{approval.OwnerAddress},
			Dapp:              dappNetwork.Dapp,
			BlockchainNetwork: &network,
			Spender:           approval.SpenderAddress,
			UpdatedAt:         approval.UpdatedAt,
		}
	}
	var out biz.UserApprovedDappsByUpdatedAt
	for _, dapp := range outMap {
		out = append(out, dapp)
	}
	sort.Sort(out)
	return out, nil
}

func (a approvalRepo) ListTokenApprovalsByDapp(ctx context.Context, dapp *biz.UserApprovedDapp) ([]*biz.TokenApproval, error) {
	var approvals []*model.Approval
	if err := a.DB(ctx).Model(&model.Approval{}).
		Where("spender_address=?", dapp.Spender).
		Where("chain_index=?", dapp.BlockchainNetwork.ChainIndex).
		Where("owner_address IN (?)", dapp.Addresses).
		Where("value > ?", 0).
		Find(&approvals).Error; err != nil {
		return nil, err
	}
	var out []*biz.TokenApproval
	for _, approval := range approvals {
		tokenApproval := &biz.TokenApproval{
			Approval: approval,
			Token: &model.TokenAsset{
				ChainIndex: approval.ChainIndex,
				Address:    approval.TokenAddress,
			},
		}
		var token model.TokenAsset
		if err := a.DB(ctx).Model(&model.TokenAsset{}).
			Where("chain_index = ?", approval.ChainIndex).
			Where("address = ?", approval.TokenAddress).
			Take(&token).Error; err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, err
		}
		if token.ID > 0 {
			tokenApproval.Token = &token
		}
		out = append(out, tokenApproval)
	}
	return out, nil
}

func (a approvalRepo) CreateOrUpdateApprovals(ctx context.Context, approvals []*model.Approval) error {
	for _, approval := range approvals {
		var old model.Approval
		if err := a.DB(ctx).Model(&model.Approval{}).
			Where("chain_index = ?", approval.ChainIndex).
			Where("owner_address = ?", approval.OwnerAddress).
			Where("spender_address = ?", approval.SpenderAddress).
			Where("token_address = ?", approval.TokenAddress).Take(&old).Error; err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				if err := a.Create(ctx, approval); err != nil {
					return err
				}
			} else {
				return err
			}
		}
		if err := a.DB(ctx).Model(&model.Approval{}).Where("id = ?", old.ID).Updates(map[string]interface{}{
			"value": approval.Value,
		}).Error; err != nil {
			return err
		}
	}
	return nil
}

func (a approvalRepo) Create(ctx context.Context, approval *model.Approval) error {
	return a.DB(ctx).Create(approval).Error
}
