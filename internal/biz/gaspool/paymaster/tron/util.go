package tron

import (
	"byd_wallet/utils"
	"fmt"
	"math/big"

	tCommon "github.com/fbsobreira/gotron-sdk/pkg/common"
	"github.com/shopspring/decimal"

	tronChain "byd_wallet/internal/biz/syncer/chain/tron"

	"github.com/btcsuite/btcutil/base58"
	"github.com/ethereum/go-ethereum/crypto"
	"github.com/fbsobreira/gotron-sdk/pkg/address"
)

func bytes2AddressString(b []byte) string {
	return address.Address(b).String()
}

func decodeTRC20TransferData(data []byte) (toAddress string, value *big.Int, err error) {
	if len(data) != 4+32*2 {
		err = fmt.Errorf("invalid trc20 transfer data length")
		return
	}

	addressParam := data[4:36]
	addressBytes := addressParam[12:32]

	toAddress = base58.CheckEncode(addressBytes, 0x41)

	valueParam := data[36:68]
	value = new(big.Int).SetBytes(valueParam)
	return
}

func decodeTRC20MethodSignature(data []byte) string {
	return tCommon.BytesToHexString(data[:4])
}

func verifyTxSignature(txHash, signature []byte, ownerAddress string) (bool, error) {
	if len(txHash) != 32 {
		return false, fmt.Errorf("tx hash length must be 32 bytes")
	}
	if len(signature) != 65 {
		return false, fmt.Errorf("signature length must be 65 bytes")
	}
	// Fix V
	if signature[64] >= 27 {
		signature[64] -= 27
	}

	pubKey, err := crypto.SigToPub(txHash, signature)
	if err != nil {
		return false, fmt.Errorf("recover pubkey: %v", err)
	}
	addr1 := utils.TronHexAddressFromPubKey(pubKey)
	addr := utils.Base58CheckEncodeTRON(addr1)
	return addr == ownerAddress, nil
}

func getAccountAvailableFee(cli *tronChain.GRPCClient, address string) (bandwidth, energy decimal.Decimal, err error) {
	res, err := cli.GetAccountResource(address)
	if err != nil {
		err = fmt.Errorf("get account resource error: %w", err)
		return
	}
	b1 := res.FreeNetLimit - res.FreeNetUsed
	b2 := res.NetLimit - res.NetUsed
	bandwidth = decimal.NewFromInt(b1 + b2)
	energy = decimal.NewFromInt(res.EnergyLimit - res.EnergyUsed)
	return
}
