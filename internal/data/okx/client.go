package okx

import (
	"bytes"
	"context"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/google/go-querystring/query"
)

// Client 定义了与OKX API交互的仓库
type Client struct {
	httpClient *http.Client
	config     *Config
	log        *log.Helper
}

type Config struct {
	BaseURL    string        `json:"base_url"`
	APIKey     string        `json:"api_key"`
	SecretKey  string        `json:"secret_key"`
	Passphrase string        `json:"passphrase"`
	ProjectID  string        `json:"project_id"`
	Timeout    time.Duration `json:"timeout"`
}

// NewClient 创建一个新的 Client 实例
func NewClient(conf *Config, logger log.Logger) *Client {
	return &Client{
		httpClient: &http.Client{Timeout: conf.Timeout},
		config:     conf,
		log:        log.NewHelper(logger),
	}
}

// signRequest 为OKX API请求生成签名和其他必要的头部信息
func (r *Client) signRequest(method, requestPath string, req any) (http.Header, string, []byte, error) {
	var (
		queryStr   string
		newReqPath = requestPath
		body       []byte
	)
	if req != nil {
		switch method {
		case http.MethodGet:
			vs, err := query.Values(req)
			if err != nil {
				return nil, "", nil, fmt.Errorf("failed to encode query string: %w", err)
			}
			queryStr = "?" + vs.Encode()
			newReqPath += queryStr
		case http.MethodPost:
			bts, err := json.Marshal(req)
			if err != nil {
				return nil, "", nil, fmt.Errorf("failed to encode request body: %w", err)
			}
			body = bts
			queryStr = string(bts)
		default:
			return nil, "", nil, fmt.Errorf("unsupported method: %s", method)
		}
	}

	timestamp := time.Now().UTC().Format("2006-01-02T15:04:05.000Z")
	prehash := timestamp + method + requestPath
	if queryStr != "" {
		prehash += queryStr
	}

	h := hmac.New(sha256.New, []byte(r.config.SecretKey))
	_, err := h.Write([]byte(prehash))
	if err != nil {
		return nil, "", nil, fmt.Errorf("failed to write hmac: %w", err)
	}
	signature := base64.StdEncoding.EncodeToString(h.Sum(nil))

	headers := http.Header{}
	headers.Set("Content-Type", "application/json")
	headers.Set("OK-ACCESS-KEY", r.config.APIKey)
	headers.Set("OK-ACCESS-SIGN", signature)
	headers.Set("OK-ACCESS-TIMESTAMP", timestamp)
	headers.Set("OK-ACCESS-PASSPHRASE", r.config.Passphrase)
	headers.Set("OK-ACCESS-PROJECT", r.config.ProjectID)

	return headers, newReqPath, body, nil
}

func (r *Client) call(ctx context.Context, method, reqPath string, req, reply any) error {
	headers, reqPath, body, err := r.signRequest(method, reqPath, req)
	if err != nil {
		return fmt.Errorf("failed to sign request: %w", err)
	}
	url := r.config.BaseURL + reqPath

	httpReq, err := http.NewRequestWithContext(ctx, method, url, bytes.NewBuffer(body))
	if err != nil {
		return fmt.Errorf("failed to create http request: %w", err)
	}
	httpReq.Header = headers

	resp, err := r.httpClient.Do(httpReq)
	if err != nil {
		return fmt.Errorf("failed to send http request: %w", err)
	}
	defer resp.Body.Close()

	respBodyBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("failed to read response body: %w", err)
	}
	r.log.Debugf("okx api request: url: %s \n req: %s \n resp: %s", url, body, respBodyBytes)

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("OKX API request failed with status code %d: %s", resp.StatusCode, string(respBodyBytes))
	}

	if err := json.Unmarshal(respBodyBytes, reply); err != nil {
		return fmt.Errorf("failed to unmarshal response body: %w", err)
	}

	return nil
}
