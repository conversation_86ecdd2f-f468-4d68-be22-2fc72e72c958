package okx

import (
	"context"
	"encoding/json"
	"fmt"
	"testing"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/stretchr/testify/require"
)

func TestClient_SupportedChains(t *testing.T) {
	cli := NewClient(&Config{
		BaseURL:    "https://web3.okx.com",
		APIKey:     "f2f528f6-5720-4800-be46-38d03f7cc732",
		SecretKey:  "5D4695712A18F097822504040A54B36F",
		Passphrase: "r!KetW#=&LJ1gbb",
		ProjectID:  "ae1352666d6a98698588e0b665c493fb",
		Timeout:    time.Second * 3,
	}, log.DefaultLogger)
	resp, err := cli.SupportedChains(context.Background())
	require.NoError(t, err)
	b, _ := json.Marshal(resp)
	fmt.Println(string(b))
}
