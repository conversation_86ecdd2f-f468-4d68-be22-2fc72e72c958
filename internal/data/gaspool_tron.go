package data

import (
	"byd_wallet/internal/biz/gaspool/paymaster/tron"
	"context"

	"github.com/shopspring/decimal"
)

type tronAsyncTaskMgr struct {
}

func NewTronAsyncTaskMgr() tron.AsyncTaskMgr {
	return &tronAsyncTaskMgr{}
}

func (t *tronAsyncTaskMgr) AddWaitGasTask(ctx context.Context, task *tron.WaitGasTask) error {
	panic("unimplemented")
}

func (t *tronAsyncTaskMgr) AllWaitGasTask(ctx context.Context) ([]*tron.WaitGasTask, error) {
	panic("unimplemented")
}

func (t *tronAsyncTaskMgr) RemoveWaitGasTask(ctx context.Context, txID uint) error {
	panic("unimplemented")
}

type tronAddressChecker struct {
}

func NewTronAddressChecker() tron.TronAddressChecker {
	return &tronAddressChecker{}
}

func (t *tronAddressChecker) IsActivatedAddress(ctx context.Context, address string) (bool, error) {
	panic("unimplemented")
}

type tronBandwidthPayWallet struct {
}

func NewTronBandwidthPayWallet() tron.TronBandwidthPayWallet {
	return &tronBandwidthPayWallet{}
}

func (w *tronBandwidthPayWallet) TransferTrx(ctx context.Context, req *tron.TransferTrxReq) (reply *tron.TransferTrxReply, err error) {
	panic("unimplemented")
}

type tronRentApi struct {
}

func NewTronRentApi() tron.TronRentApi {
	return &tronRentApi{}
}

func (t *tronRentApi) GetEnergyPrice(ctx context.Context, req *tron.GetEnergyPriceReq) (price decimal.Decimal, err error) {
	panic("unimplemented")
}

func (t *tronRentApi) BuyEnergy(ctx context.Context, req *tron.BuyEnergyReq) (reply *tron.BuyEnergyReply, err error) {
	panic("unimplemented")
}
