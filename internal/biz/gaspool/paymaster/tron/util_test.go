package tron

import (
	"encoding/hex"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestVerifyTxSignature(t *testing.T) {
	txHash, _ := hex.DecodeString("6f99e0e20a39a398ad468977b3bec726ed09ff45ae5f44d27589389134fdc2bf")
	signature, _ := hex.DecodeString("8e13662e12e662154971f06ebf6bbac10264ffb4429096df6b4c026eb36921d36bdd378fabab3f05caadcdcb69049b28f58f1ca28b8308f78a41896cddcd07f400")
	addr := "TMBHCWVLYQYUy688EByoduJnzeVnkwnQRY"
	ok, err := verifyTxSignature(txHash, signature, addr)
	assert.NoError(t, err)
	assert.True(t, ok)
}
