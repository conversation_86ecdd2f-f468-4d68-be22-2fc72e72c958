package server

import (
	walletv1 "byd_wallet/api/wallet/v1"
	"byd_wallet/internal/conf"
	"byd_wallet/internal/server/middleware/localize"
	"byd_wallet/internal/server/middleware/validate"
	"byd_wallet/internal/server/swagger"
	"byd_wallet/internal/service"

	"github.com/go-kratos/kratos/v2/encoding/json"
	"github.com/go-kratos/kratos/v2/transport"
	"github.com/nicksnyder/go-i18n/v2/i18n"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/types/known/anypb"

	stdhttp "net/http"

	"github.com/go-kratos/kratos/v2/errors"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-kratos/kratos/v2/middleware/logging"
	"github.com/go-kratos/kratos/v2/middleware/recovery"
	"github.com/go-kratos/kratos/v2/transport/http"
)

// NewHTTPServer new an HTTP server.
func NewHTTPServer(c *conf.Server,
	user *service.UserService,
	wallet *service.WalletSrvService,
	tron *service.TronService,
	market *service.MarketService,
	dapp *service.DappService,
	swap *service.SwapService,
	gaspool *service.GasPoolService,
	logger log.Logger) *http.Server {
	var opts = []http.ServerOption{
		http.Middleware(
			recovery.Recovery(),
			logging.Server(logger),
			validate.ProtoValidate(),
			localize.I18N(),
		),
		http.ResponseEncoder(ResponseEncoder),
		http.ErrorEncoder(ErrorEncoder),
	}
	if c.Http.Network != "" {
		opts = append(opts, http.Network(c.Http.Network))
	}
	if c.Http.Addr != "" {
		opts = append(opts, http.Address(c.Http.Addr))
	}
	if c.Http.Timeout != nil {
		opts = append(opts, http.Timeout(c.Http.Timeout.AsDuration()))
	}
	srv := http.NewServer(opts...)
	walletv1.RegisterUserSrvHTTPServer(srv, user)
	walletv1.RegisterWalletSrvHTTPServer(srv, wallet)
	walletv1.RegisterTronSrvHTTPServer(srv, tron)
	walletv1.RegisterMarketSrvHTTPServer(srv, market)
	walletv1.RegisterDappSrvHTTPServer(srv, dapp)
	walletv1.RegisterSwapServiceHTTPServer(srv, swap)
	walletv1.RegisterGasPoolSrvHTTPServer(srv, gaspool)
	// 创建现代化的Swagger UI处理器，支持中文注释显示
	swaggerHandler := swagger.NewHandler("api/wallet/openapi.yaml", "/q")
	// 设置swagger-ui前缀，访问swagger-ui路由为/q/swagger-ui
	srv.HandlePrefix("/q/", swaggerHandler)
	return srv
}

const (
	baseContentType = "application"
)

// ContentType returns the content-type with base prefix.
func ContentType(subtype string) string {
	return baseContentType + "/" + subtype
}

func ResponseEncoder(w http.ResponseWriter, r *http.Request, v any) error {
	if v == nil {
		return nil
	}
	if rd, ok := v.(http.Redirector); ok {
		url, code := rd.Redirect()
		stdhttp.Redirect(w, r, url, code)
		return nil
	}
	// 最终响应结构
	resp := &walletv1.HttpResponse{
		Code:    0,
		Message: "ok",
	}
	json.MarshalOptions = protojson.MarshalOptions{
		EmitUnpopulated: true, //默认值不忽略
		UseProtoNames:   true, //使用proto name返回http字段
	}
	switch msg := v.(type) {
	case proto.Message:
		data, err := anypb.New(msg)
		if err != nil {
			return walletv1.ErrorInternalServer("new anypb failed: %v", err)
		}
		resp.Data = data
	default:
		return walletv1.ErrorInternalServer("unknown type: %T", v)
	}

	codec, _ := http.CodecForRequest(r, "Accept")
	data, err := codec.Marshal(resp)
	if err != nil {
		return walletv1.ErrorInternalServer("marshal response failed: %v", err)
	}
	w.Header().Set("Content-Type", ContentType(codec.Name()))
	_, err = w.Write(data)
	if err != nil {
		return walletv1.ErrorInternalServer("write response failed: %v", err)
	}
	return nil
}

func ErrorEncoder(w http.ResponseWriter, r *http.Request, err error) {
	se := errors.FromError(err) // 将 err 转换为 kratos/errors.Error

	localizedMessage := se.Reason // 使用Reason作为默认消息

	ctx := r.Context()
	if tr, ok := transport.FromServerContext(ctx); ok {
		accept := tr.RequestHeader().Get("accept-language")
		localizer := i18n.NewLocalizer(localize.Bundle, accept)
		localizeConfig := &i18n.LocalizeConfig{
			// 使用 Reason 作为 MessageID
			MessageID: se.Reason,
		}
		if translatedMsg, translateErr := localizer.Localize(localizeConfig); translateErr == nil {
			localizedMessage = translatedMsg
		}
	}

	if localizedMessage == "" {
		localizedMessage = err.Error()
	}

	// 构建 HttpResponse 结构体
	// 这里的 code 可以是你自定义的业务错误码，例如直接使用 Kratos Error 的 code 或定义映射
	// 这里我们使用 Kratos Error 的 Code 作为业务 code，但实际 HTTP 状态码是 200
	resp := &walletv1.HttpResponse{
		Code:    se.Code,
		Message: localizedMessage,
	}

	codec, _ := http.CodecForRequest(r, "Accept")
	body, err := codec.Marshal(resp)
	if err != nil {
		w.WriteHeader(stdhttp.StatusInternalServerError)
		return
	}
	w.Header().Set("Content-Type", ContentType(codec.Name()))
	w.WriteHeader(stdhttp.StatusOK) // ！！！强制设置为 200 OK
	_, _ = w.Write(body)
}
