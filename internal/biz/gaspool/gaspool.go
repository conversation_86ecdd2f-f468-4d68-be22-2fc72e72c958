package gaspool

import (
	"byd_wallet/model"
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/shopspring/decimal"
)

type SendTxReq struct {
	ChainIndex int64
	RawTxHex   string
	TxType     model.GasPoolTxType
}

type SendTxReply struct {
	ChainIndex int64
	TxHash     string
}

type UserTx struct {
	ChainIndex  int64
	TxHash      string
	TxHashBytes []byte
	TxType      model.GasPoolTxType
	From        string
	To          string
	Value       decimal.Decimal
	ValueUSDT   decimal.Decimal
	FeeLimit    decimal.Decimal
	Contract    string
	RawTxHex    string
	RawTxBytes  []byte
	UserID      uint
	BroadcastTx interface{}
}

type UserTxGas struct {
	Gas           decimal.Decimal
	GasUSDT       decimal.Decimal
	Price         decimal.Decimal
	PriceTimeUnix int64
	// only tron
	Bandwidth      decimal.Decimal
	BandwidthPrice decimal.Decimal
	Energy         decimal.Decimal
	EnergyPrice    decimal.Decimal
	ActivateFee    decimal.Decimal
}

type UserLocker interface {
	TryLock(ctx context.Context, userID uint, expire time.Duration) error
	UnLock(ctx context.Context, userID uint) error
}

type Paymaster interface {
	DecodeUserTx(ctx context.Context, rawTxHex string, txType model.GasPoolTxType) (*UserTx, error)
	VerifyUserTxSignature(ctx context.Context, tx *UserTx) (bool, error)
	// EstimateGasUSDT estimates gas used USDT
	// NOTE: GasUSDT = Gas / (10 ^ Gas's Decimals) * GasPriceUSDT * (10 ^ USDT's Decimals)
	EstimateGas(ctx context.Context, tx *UserTx) (*UserTxGas, error)
	AuditUserTx(ctx context.Context, tx *UserTx, gas *UserTxGas) (bool, error)
	SendDepositTx(ctx context.Context, tx *UserTx) error
	SendSponsorTx(ctx context.Context, tx *model.GasPoolSponsorTx) error
}

type PaymasterFactory interface {
	GetPaymaster(ctx context.Context, chainIndex int64) (Paymaster, error)
}

type Repo interface {
	DepositGasPool(ctx context.Context, userID uint, amount decimal.Decimal, chainIndex int64, txHash string) (*model.GasPoolFlow, error)
	ReduceGasPool(ctx context.Context, userID uint, amount decimal.Decimal, chainIndex int64, txHash string) (*model.GasPoolFlow, error)
	RefundGasPool(ctx context.Context, reduceFlowID uint, amount decimal.Decimal) (*model.GasPoolFlow, error)

	SaveGasPoolSponsorTx(ctx context.Context, tx *model.GasPoolSponsorTx) (*model.GasPoolSponsorTx, error)
	UpdateGasPoolSponsorTx(ctx context.Context, tx *model.GasPoolSponsorTx) error

	FindUserIDByAddress(ctx context.Context, chainIndex int64, address string) (userID uint, err error)
	FindDepositTokenByAddress(ctx context.Context, chainIndex int64, address string) (*model.GasPoolDepositToken, error)
	ExistsDepositReceiverAddress(ctx context.Context, chainIndex int64, address string) (bool, error)
}

type Usecase struct {
	log        *log.Helper
	userLocker UserLocker
	pmf        PaymasterFactory
	repo       Repo
}

func NewUsecase(logger log.Logger,
	repo Repo,
	userLocker UserLocker,
	pmf PaymasterFactory) *Usecase {
	return &Usecase{
		pmf:        pmf,
		log:        log.NewHelper(logger),
		repo:       repo,
		userLocker: userLocker,
	}
}

func (uc *Usecase) SendTx(ctx context.Context, req *SendTxReq) (*SendTxReply, error) {
	paymaster, err := uc.pmf.GetPaymaster(ctx, req.ChainIndex)
	if err != nil {
		return nil, err
	}
	userTx, err := paymaster.DecodeUserTx(ctx, req.RawTxHex, req.TxType)
	if err != nil {
		return nil, err
	}
	ok, err := paymaster.VerifyUserTxSignature(ctx, userTx)
	if err != nil {
		return nil, err
	}
	if !ok {
		return nil, errors.New("invalid user tx signature")
	}
	if userTx.TxType.IsDepositGasPool() {
		// check deposit token
		token, err := uc.repo.FindDepositTokenByAddress(ctx, userTx.ChainIndex, userTx.Contract)
		if err != nil {
			return nil, fmt.Errorf("deposit token not found: %v: %s", err, userTx.Contract)
		}
		// check deposit amount
		if userTx.Value.LessThan(token.MinValue) {
			return nil, fmt.Errorf("deposit amount is too small: %s: %s", userTx.Value.String(), token.MinValue.String())
		}
		// check tx receiver
		ok, err := uc.repo.ExistsDepositReceiverAddress(ctx, userTx.ChainIndex, userTx.To)
		if err != nil {
			return nil, err
		}
		if !ok {
			return nil, fmt.Errorf("deposit receiver not found: %s", userTx.To)
		}
	}
	gpGas, err := paymaster.EstimateGas(ctx, userTx)
	if err != nil {
		return nil, err
	}
	userID, err := uc.repo.FindUserIDByAddress(ctx, userTx.ChainIndex, userTx.From)
	if err != nil {
		return nil, err
	}

	// TODO: check user tx expire: block num, or nonce
	// TODO: without gas: check value >= gas

	ok, err = paymaster.AuditUserTx(ctx, userTx, gpGas)
	if err != nil {
		return nil, err
	}
	if !ok {
		return nil, errors.New("user tx not allowed")
	}

	if err = uc.userLocker.TryLock(ctx, userID, 30*time.Minute); err != nil {
		return nil, err
	}
	defer uc.userLocker.UnLock(ctx, userID)

	userGasPool := userTx.TxType.IsUseGasPool()
	if userGasPool {
		stx := &model.GasPoolSponsorTx{
			ChainIndex:     userTx.ChainIndex,
			UserID:         userID,
			RawTxHex:       userTx.RawTxHex,
			TxHash:         userTx.TxHash,
			TxType:         userTx.TxType,
			From:           userTx.From,
			To:             userTx.To,
			Contract:       userTx.Contract,
			Value:          userTx.Value,
			ValueUSDT:      userTx.ValueUSDT,
			Price:          gpGas.Price,
			PriceTimeUnix:  gpGas.PriceTimeUnix,
			Gas:            gpGas.Gas,
			GasUSDT:        gpGas.GasUSDT,
			Bandwidth:      gpGas.Bandwidth,
			BandwidthPrice: gpGas.BandwidthPrice,
			Energy:         gpGas.Energy,
			EnergyPrice:    gpGas.EnergyPrice,
			Status:         model.GasPoolTxStatusInit,
		}
		if !userTx.TxType.IsPreReduceGasPool() {
			flow, err := uc.repo.ReduceGasPool(ctx,
				userID,
				stx.GasUSDT,
				stx.ChainIndex,
				stx.TxHash)
			if err != nil {
				return nil, err
			}
			stx.ReduceFlowID = flow.ID
		}
		stx, err = uc.repo.SaveGasPoolSponsorTx(ctx, stx)
		if err != nil {
			return nil, err
		}
		err = paymaster.SendSponsorTx(ctx, stx)
		if err != nil {
			return nil, err
		}
	} else {
		err = paymaster.SendDepositTx(ctx, userTx)
		if err != nil {
			return nil, err
		}
	}
	return &SendTxReply{
		ChainIndex: userTx.ChainIndex,
		TxHash:     userTx.TxHash,
	}, nil
}
