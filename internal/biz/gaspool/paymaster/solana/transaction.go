package solana

import (
	"byd_wallet/internal/biz/gaspool"
	"context"
	"fmt"

	"github.com/gagliardetto/solana-go"
	"github.com/shopspring/decimal"
)

// transaction.go - Solana paymaster 交易处理相关功能
// 包含交易解码、签名、准备等核心交易处理逻辑

// decodeTransferTx 解码转账交易
// 参数：
//   - ctx: 上下文对象
//   - rawTx: 原始交易十六进制字符串
//
// 返回：
//   - *gaspool.UserTx: 解码后的用户交易
//   - error: 错误信息
func (pm *Paymaster) decodeTransferTx(ctx context.Context, rawTx string) (*gaspool.UserTx, error) {
	var tx gaspool.UserTx
	transaction, err := pm.prepareTransaction(rawTx)
	if err != nil {
		return nil, fmt.Errorf("prepareTransaction fail: %v", err)
	}
	// 解析交易消息获取交易详情
	txInfos, err := pm.parseMessage(&transaction.Message)
	if err != nil {
		return nil, fmt.Errorf("parseMessage error: %v", err)
	}

	if len(txInfos) == 0 {
		return &tx, nil
	}

	if len(transaction.Signatures) > 0 {
		tx.TxHash = transaction.Signatures[0].String()
	}
	// 使用第一个转账指令的信息
	txInfo := txInfos[0]
	tx.From = txInfo.From
	tx.To = txInfo.To
	tx.Value = decimal.NewFromInt(int64(txInfo.Value))
	tx.Contract = txInfo.Mint // SPL代币合约地址，SOL转账时为空

	pm.log.Debugf("成功解码转账交易，合约地址%v，从 %s 到 %s，金额: %s", txInfo.Mint, txInfo.From, txInfo.To, tx.Value.String())

	return &tx, nil
}

// signTransaction 为交易签名（需要用户签名信息）
// 参数说明：
//   - tx: 需要签名的交易对象
//   - rawTx: 包含用户签名信息的原始交易数据
func (pm *Paymaster) signTransaction(tx *solana.Transaction) error {
	// 输入参数验证
	if tx == nil {
		return fmt.Errorf("invalid tx")
	}

	// 序列化交易消息用于签名
	msgBytes, err := tx.Message.MarshalBinary()
	if err != nil {
		return wrapError("serialize transaction message", err)
	}

	// 使用统一的签名创建函数
	feePayerSig, err := pm.createPaymasterSignature(msgBytes)
	if err != nil {
		return wrapError("create paymaster signature", err)
	}

	// 确定哪些账户需要签名
	signers := pm.getRequiredSigners(tx.Message)

	// 设置交易签名
	if err := pm.setTransactionSignatures(tx, signers, feePayerSig); err != nil {
		return fmt.Errorf("set pay signer fail : %w", err)
	}

	// 验证所有签名
	if err := tx.VerifySignatures(); err != nil {
		return fmt.Errorf("varify signatures fail : %w", err)
	}

	return nil
}

// prepareTransaction 准备交易对象
// 参数：
//   - rawTxHex: 原始交易十六进制字符串
//
// 返回：
//   - *solana.Transaction: 准备好的交易对象
//   - error: 错误信息
func (pm *Paymaster) prepareTransaction(rawTxHex string) (*solana.Transaction, error) {
	tx, err := solana.TransactionFromBase64(rawTxHex)
	if err != nil {
		return nil, fmt.Errorf("failed to generate transaction: %w", err)
	}

	return tx, nil
}

// setTransactionSignatures 设置交易签名
// 参数说明：
//   - tx: 需要设置签名的交易对象
//   - signers: 需要签名的账户公钥列表
//   - feePayerSig: 费用支付者（paymaster）的签名
func (pm *Paymaster) setTransactionSignatures(tx *solana.Transaction, signers []solana.PublicKey,
	feePayerSig solana.Signature) error {
	// 验证签名参数
	if feePayerSig.IsZero() {
		return fmt.Errorf("get feePayerSig fail")
	}
	// 获取paymaster公钥
	paymasterPub, err := pm.getPayPublic()
	if err != nil {
		return fmt.Errorf("get paymaster public fail: %w", err)
	}

	// 记录已处理的签名者类型
	var processedPaymaster bool

	// 遍历签名者列表，为每个签名者设置对应的签名
	for i, signer := range signers {
		switch {
		case signer.Equals(paymasterPub):
			// 设置paymaster签名
			tx.Signatures[i] = feePayerSig
			processedPaymaster = true
			pm.log.Debugf("已设置paymaster签名，索引: %d, 公钥: %s", i, signer.String())
		}
	}

	// 验证必要的签名者是否都已处理
	if !processedPaymaster {
		return fmt.Errorf("not hava paymaster siner: %s", paymasterPub.String())
	}

	// 使用统一的签名验证函数
	if err := validateTransactionSignatures(tx, len(signers)); err != nil {
		return wrapError("validate transaction signatures", err)
	}

	pm.log.Infof("交易签名设置完成，共处理 %d 个签名，paymaster: %s",
		len(signers), paymasterPub.String())
	return nil
}
