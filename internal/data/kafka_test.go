package data

import (
	"byd_wallet/internal/biz"
	"byd_wallet/internal/conf"
	"context"
	"testing"
	"time"

	"github.com/segmentio/kafka-go"
	"github.com/stretchr/testify/assert"
)

func TestTxSyncEventPublisher(t *testing.T) {
	s := assert.New(t)

	kafkaCfg := &conf.Data_Kafka{
		Brokers:           []string{"localhost:9092"},
		TokenTopic:        "token_topic",
		HoldNewTokenTopic: "hold_new_token_topic",
	}
	reader := kafka.NewReader(kafka.ReaderConfig{
		Brokers:     kafkaCfg.Brokers,
		GroupTopics: []string{kafkaCfg.TokenTopic, kafkaCfg.HoldNewTokenTopic},
		GroupID:     "test-multi-topic-consumer-group",
	})
	defer reader.Close()
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()
	go func() {
		for {
			select {
			case <-ctx.Done():
				return
			default:
				kmsg, err := reader.ReadMessage(ctx)
				if err != nil {
					t.Logf("read message error: %v", err)
					continue
				}
				t.Logf("[%s] receive message: %s", kmsg.Topic, string(kmsg.Value))
			}
		}
	}()

	publisher, cf, err := NewTxSyncEventPublisher(&conf.Data{
		Kafka: kafkaCfg,
	})
	s.NoError(err)
	defer cf()
	s.Error(publisher.Publish(ctx, "unknown event"))

	s.NoError(publisher.Publish(ctx, &biz.TxSyncEventHoldNewToken{
		List: []*struct {
			WalletAddress string
			ChainIndex    int64
			TokenAddress  string
		}{
			{
				WalletAddress: "0x123",
				ChainIndex:    1,
				TokenAddress:  "0x456",
			},
			{
				WalletAddress: "0x123",
				ChainIndex:    2,
				TokenAddress:  "0x9996333",
			},
		},
	}))

	s.NoError(publisher.Publish(ctx, &biz.TxSyncEventTokenDeploy{
		ChainIndex: 99,
		ChainID:    "99",
		Address:    "0x99",
		DeployAt:   time.Now().Unix(),
	}))

	time.Sleep(10 * time.Second)
	t.Log("stop kafka consumer")
	cancel()
	time.Sleep(time.Second)
}
