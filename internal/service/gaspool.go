package service

import (
	v1 "byd_wallet/api/wallet/v1"
	"byd_wallet/internal/biz/gaspool"
	"context"
)

type GasPoolService struct {
	v1.UnimplementedGasPoolSrvServer

	uc *gaspool.Usecase
}

func NewGasPoolService(uc *gaspool.Usecase) *GasPoolService {
	return &GasPoolService{uc: uc}
}

// 可充值币种列表
func (s *GasPoolService) ListDepositToken(ctx context.Context, req *v1.ListDepositTokenReq) (*v1.ListDepositTokenReply, error) {
	panic("not implemented") // TODO: Implement
}

// 查询GasPool余额
func (s *GasPoolService) GetGasPoolBalance(ctx context.Context, req *v1.GetGasPoolBalanceReq) (*v1.GetGasPoolBalanceReply, error) {
	panic("not implemented") // TODO: Implement
}

// 查询GasPool统计数据
func (s *GasPoolService) GetGasPoolStat(ctx context.Context, req *v1.GetGasPoolStatReq) (*v1.GetGasPoolStatReply, error) {
	panic("not implemented") // TODO: Implement
}

// GasPool消费记录列表
func (s *GasPoolService) ListGasPoolConsumeRecord(ctx context.Context, req *v1.ListGasPoolConsumeRecordReq) (*v1.ListGasPoolConsumeRecordReply, error) {
	panic("not implemented") // TODO: Implement
}

// GasPool财务记录列表
func (s *GasPoolService) ListGasPoolCashFlowRecord(ctx context.Context, req *v1.ListGasPoolCashFlowRecordReq) (*v1.ListGasPoolCashFlowRecordReply, error) {
	panic("not implemented") // TODO: Implement
}

// 获取paymaster address
func (s *GasPoolService) GetPaymaster(ctx context.Context, req *v1.GetPaymasterReq) (*v1.GetPaymasterReply, error) {

	return &v1.GetPaymasterReply{}, nil
}
